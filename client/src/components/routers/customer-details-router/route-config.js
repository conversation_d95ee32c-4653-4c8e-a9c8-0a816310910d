import React, { lazy } from 'react';
import { PRODUCT_CONFIG } from '../../pages/document-tickets/config';
import { customerDetailsNavIds } from '../../layouts/customer-details-layout/nav-config';
import CustomerBasicDetail from '../../pages/customer-details/customer-basic-details';
import CustomerTransactions from '../../pages/customer-details/customer-transactions';
import CustomerActiveSIP from '../../pages/customer-details/customer-active-sip';
import CustomerCancelledSIP from '../../pages/customer-details/customer-cancelled-sip';
import CustomerPortfolio from '../../pages/customer-details/customer-portfolio';
import CustomerExternalPortfolio from '../../pages/customer-details/customer-external-portfolio';
import CustomerPaymentModes from '../../pages/customer-details/customer-payment-modes';
import CustomerPaymentModesPage from '../../pages/customer-details/customer-mf-payment-modes';
import CustomerForms from '../../pages/customer-details/customer-forms';
import CustomerStatements from '../../pages/customer-details/CustomerStatements';
import EqOrdersContainer from '../../pages/customer-details/eq-orders/EqOrdersContainer';
import EqChargesContainer from '../../pages/customer-details/eq-charges/EqChargesContainer';
import EqPositionssContainer from '../../pages/customer-details/eq-positions/EqPositionsContainer';
import EqFundsContainer from '../../pages/customer-details/eq-funds/EqFundsContainer';
import EqStatements from '../../pages/customer-details/customer-statements/equity_statements/EqStatements';
import NpsSipLayout from '../../pages/customer-details/customer-active-sip/nps-sip/npsSipLayout';
import NpsPortfolioLayout from '../../pages/customer-details/customer-portfolio/nps-portfolio/npsPortfolioLayout';
import NpsStatements from '../../pages/customer-details/customer-statements/nps-statements/NpsStatements';
import EpfComponent from '../../pages/customer-details/epf/EpfContainer';
import CustomerIpoApplications from '../../pages/customer-details/eq-ipo-applications';
import CustomerIPOHniApplications from '../../pages/customer-details/eq-hni-ipo-applications';
import UserFeedback from '../../pages/customer-details/customer-feedback';
import EqSubsLayout from '../../pages/customer-details/eq-subscription/EqSubsLayout';
import EqMarginPledgeLayout from '../../pages/customer-details/eq-margin-pledge/EqMarginPledge';
import EqBuybackLayout from '../../pages/customer-details/eq-buyback/EqBuybackContainer';
import WealthDeskPortfolio from '../../pages/customer-details/eq-wealthdesk-portfolio/EqWealthDeskPortfolio';
import CustomerRefferal from '../../pages/customer-details/customer-refferal/CustomerRefferalContainer';
import ActiveCampaigns from '../../pages/customer-details/active-campaigns/ActiveCampaigns';
import EqPortfolioContainer from '../../pages/customer-details/eq-portfolio/EqPortfolioContainer';
import DematHoldings from '../../pages/customer-details/demat-holdings';
import CustomerRewardsData from '../../pages/customer-details/customer-rewards-data/CustomerRewardsContainer.jsx';

const CustomerInvestmentReadinessPage = lazy(() => import(/* webpackChunkName: "investmentReadiness" */ '../../pages/customer-details/customer-investment-readiness'));

const DefaultTemplate = () => (
  <div>Component is not made yet.</div>
);

const CUSTOMER_DETAILS_ROUTES = {
  BASIC_DETAILS: (props) => ({
    path: '/basic-details',
    isExact: true,
    navLinkId: customerDetailsNavIds.BASIC_DETAILS,
    component: CustomerBasicDetail,
    componentProps: {
      ...props.customerDetails,
    },
    isDefault: true,
    permissions: ['BASIC_DETAILS'],
  }),
  CORE_IR_READINESS: (props) => ({
    path: '/core-ir-readiness',
    isExact: true,
    navLinkId: customerDetailsNavIds.CORE_IR_READINESS,
    component: CustomerInvestmentReadinessPage,
    componentProps: {
      customerId: props.selectedCustomerId,
      customerName: props.customerDetails.customerDetails ? props.customerDetails.customerDetails.name : null,
      UserType: props.UserType,
      location: props.location,
      investmentType: 'MF',
    },
    permissions: ['INVESTMENT_READINESS'],
  }),
  EQUITY_IR_READINESS: (props) => ({
    path: '/equity-ir-readiness',
    isExact: true,
    navLinkId: customerDetailsNavIds.EQUITY_IR_READINESS,
    component: CustomerInvestmentReadinessPage,
    componentProps: {
      customerId: props.selectedCustomerId,
      customerName: props.customerDetails.customerDetails ? props.customerDetails.customerDetails.name : null,
      UserType: props.UserType,
      location: props.location,
      investmentType: 'EQUITY',
    },
    permissions: ['EQ_INV_READINESS'],
  }),
  NPS_IR_READINESS: (props) => ({
    path: '/nps-ir-readiness',
    isExact: true,
    navLinkId: customerDetailsNavIds.NPS_IR_READINESS,
    component: CustomerInvestmentReadinessPage,
    componentProps: {
      customerId: props.selectedCustomerId,
      UserType: props.UserType,
      location: props.location,
      investmentType: 'NPS',
    },
    permissions: ['NPS_IR_READINESS'],
  }),
  REFFERAL_SOURCE: (props) => ({
    path: '/customer-refferal',
    isExact: true,
    navLinkId: customerDetailsNavIds.REFFERAL_SOURCE,
    component: CustomerRefferal,
    componentProps: {
      customerId: props.selectedCustomerId,
      UserType: props.UserType,
      location: props.location,
      investmentType: 'NPS',
    },
    permissions: ['EQ_ORDERS'],
  }),
  REWARDS_DATA: (props) => ({
    path: '/customer-rewards-data',
    isExact: true,
    navLinkId: customerDetailsNavIds.CUSTOMER_REWARDS,
    component: CustomerRewardsData,
    componentProps: {
      customerId: props.selectedCustomerId,
      UserType: props.UserType,
      location: props.location,
    },
    permissions: ['EQ_ORDERS'],
  }),
  USER_FEEDBACK: (props) => ({
    path: '/user-feedback',
    navLinkId: customerDetailsNavIds.USER_FEEDBACK,
    component: UserFeedback,
    componentProps: {
      customerId: props.selectedCustomerId,
      customerName: props.customerDetails.customerDetails ? props.customerDetails.customerDetails.name : null,
      UserType: props.UserType,
      location: props.location,
      investmentType: 'MF',
    },
    permissions: ['USER_FEEDBACK'],
  }),
  NFT_APPROVAL: (props) => ({
    path: '/nft-approval',
    isExact: true,
    navLinkId: customerDetailsNavIds.NFT_APPROVAL,
    component: CustomerInvestmentReadinessPage,
    componentProps: {
      customerId: props.selectedCustomerId,
      UserType: props.UserType,
      location: props.location,
      investmentType: 'NFT',
    },
    permissions: ['NFT_APPROVAL'],
  }),
  PAYMENT_MODES: (props) => ({
    path: '/payment-modes',
    isExact: true,
    navLinkId: customerDetailsNavIds.PAYMENT_MODES,
    component: CustomerPaymentModes,
    componentProps: {
      customerName: props.customerDetails ? props.customerDetails.name : null,
      customerId: props.selectedCustomerId,
    },
    permissions: ['PAYMENT_MODES'],
  }),
  PAYMENT_MODES_MF: (props) => ({
    path: '/payment-modes-mf',
    isExact: true,
    navLinkId: customerDetailsNavIds.PAYMENT_MODES_MF,
    component: CustomerPaymentModesPage,
    componentProps: {
      customerName: props.customerDetails.customerDetails ? props.customerDetails.customerDetails.name : null,
      customerId: props.selectedCustomerId,
      investmentType: PRODUCT_CONFIG.MF.product,
    },
    permissions: ['PAYMENT_MODES'],
  }),
  PAYMENT_MODES_EQUITY: (props) => ({
    path: '/payment-modes-equity',
    isExact: true,
    navLinkId: customerDetailsNavIds.PAYMENT_MODES_EQUITY,
    component: CustomerPaymentModesPage,
    componentProps: {
      customerName: props.customerDetails.customerDetails ? props.customerDetails.customerDetails.name : null,
      customerId: props.selectedCustomerId,
      investmentType: PRODUCT_CONFIG.EQUITY.product,
    },
    permissions: ['PAYMENT_MODES'],
  }),
  FORMS: (props) => ({
    path: '/forms',
    isExact: true,
    navLinkId: customerDetailsNavIds.FORMS,
    component: CustomerForms,
    componentProps: {
      customerName: props.customerDetails.name,
      customerId: props.selectedCustomerId,
    },
    permissions: ['FORMS'],
  }),

  // MF
  PORTFOLIO: (props) => ({
    path: '/portfolio',
    isExact: true,
    navLinkId: customerDetailsNavIds.PORTFOLIO,
    component: CustomerPortfolio,
    componentProps: {
      customerId: props.selectedCustomerId,
    },
    permissions: ['PORTFOLIO'],
  }),
  DEMAT_HOLDINGS: (props) => ({
    path: '/demat-holdings',
    isExact: true,
    navLinkId: customerDetailsNavIds.DEMAT_HOLDINGS,
    component: DematHoldings,
    componentProps: {
      customerId: props.selectedCustomerId,
    },
    permissions: ['PORTFOLIO'],
  }),
  EXTERNAL_PORTFOLIO: (props) => ({
    path: '/external-portfolio',
    isExact: true,
    navLinkId: customerDetailsNavIds.EXTERNAL_PORTFOLIO,
    component: CustomerExternalPortfolio,
    componentProps: {
      customerId: props.selectedCustomerId,
    },
    permissions: ['EXTERNAL_PORTFOLIO'],
  }),
  ACTIVE_SIPS: (props) => ({
    path: '/active-sip',
    isExact: true,
    navLinkId: customerDetailsNavIds.ACTIVE_SIPS,
    component: CustomerActiveSIP,
    componentProps: {
      customerId: props.selectedCustomerId,
      history: props.history,
    },
    permissions: ['ACTIVE_SIPS'],
  }),
  CANCELLED_SIPS: (props) => ({
    path: '/cancelled-sip',
    isExact: true,
    navLinkId: customerDetailsNavIds.CANCELLED_SIPS,
    component: CustomerCancelledSIP,
    componentProps: {
      customerId: props.selectedCustomerId,
      history: props.history,
    },
    permissions: ['ACTIVE_SIPS'],
  }),
  TRANSACTIONS: (props) => ({
    path: '/transactions',
    isExact: true,
    navLinkId: customerDetailsNavIds.TRANSACTIONS,
    component: CustomerTransactions,
    componentProps: {
      customerId: props.selectedCustomerId,
      userType: props.userType,
      history: props.history,
      product: 'MF',
    },
    permissions: ['TRANSACTIONS'],
  }),
  STATEMENTS: (props) => ({
    path: '/statements',
    isExact: true,
    navLinkId: customerDetailsNavIds.STATEMENTS,
    component: CustomerStatements,
    componentProps: {
      history: props.history,
      customerId: props.selectedCustomerId,
      customerDetails: props.customerDetails,
    },
    permissions: ['STATEMENTS'],
  }),

  // NPS
  NPS_PORTFOLIO: (props) => ({
    path: '/nps-portfolio',
    isExact: true,
    navLinkId: customerDetailsNavIds.NPS_PORTFOLIO,
    component: NpsPortfolioLayout,
    componentProps: {
      customerId: props.selectedCustomerId,
    },
    permissions: ['NPS_PORTFOLIO'],
  }),
  NPS_ACTIVE_SIPS: (props) => ({
    path: '/nps-active-sip',
    isExact: true,
    navLinkId: customerDetailsNavIds.NPS_ACTIVE_SIPS,
    component: NpsSipLayout,
    componentProps: {
      customerId: props.selectedCustomerId,
      history: props.history,
    },
    permissions: ['NPS_SIP'],
  }),
  NPS_TRANSACTIONS: (props) => ({
    path: '/nps-transactions',
    isExact: true,
    navLinkId: customerDetailsNavIds.NPS_TRANSACTIONS,
    component: CustomerTransactions,
    componentProps: {
      customerId: props.selectedCustomerId,
      userType: props.userType,
      history: props.history,
      product: 'NPS',
    },
    permissions: ['NPS_TRANSACTION'],
  }),
  NPS_STATEMENTS: (props) => ({
    path: '/nps-statements',
    isExact: true,
    navLinkId: customerDetailsNavIds.NPS_STATEMENTS,
    component: NpsStatements,
    componentProps: {
      history: props.history,
      customerId: props.selectedCustomerId,
      customerDetails: props.customerDetails,
    },
    permissions: ['NPS_STATEMENTS'],
  }),

  // EF
  EQ_ORDERS: (props) => ({
    path: '/eq-orders',
    isExact: true,
    navLinkId: customerDetailsNavIds.EQ_ORDERS,
    component: EqOrdersContainer,
    componentProps: {
      history: props.history,
      customerId: props.selectedCustomerId,
    },
    permissions: ['EQ_ORDERS'],
  }),
  EQ_POSITIONS: (props) => ({
    path: '/eq-positions',
    isExact: true,
    navLinkId: customerDetailsNavIds.EQ_POSITIONS,
    component: EqPositionssContainer,
    componentProps: {
      customerId: props.selectedCustomerId,
    },
    permissions: ['EQ_POSITIONS'],
  }),
  EQ_CHARGES: (props) => ({
    path: '/eq-charges',
    isExact: true,
    navLinkId: customerDetailsNavIds.EQ_CHARGES,
    component: EqChargesContainer,
    componentProps: {
      customerId: props.selectedCustomerId,
      history: props.history,
    },
    permissions: ['EQ_CHARGES'],
  }),
  EQ_PORTFOLIO: (props) => ({
    path: '/eq-portfolio',
    isExact: true,
    navLinkId: customerDetailsNavIds.EQ_PORTFOLIO,
    component: EqPortfolioContainer,
    componentProps: {
      customerId: props.selectedCustomerId,
      userType: props.userType,
      history: props.history,
    },
    permissions: ['EQ_PORTFOLIO'],
  }),
  EQ_WEALTHDESK_PORTFOLIO: (props) => ({
    path: '/eq-wealthdesk-portfolio',
    isExact: true,
    navLinkId: customerDetailsNavIds.EQ_WEALTHDESK_PORTFOLIO,
    component: WealthDeskPortfolio,
    componentProps: {
      customerId: props.selectedCustomerId,
      userType: props.userType,
      history: props.history,
    },
    permissions: ['EQ_PORTFOLIO'],
  }),
  EQ_FUNDS: (props) => ({
    path: '/eq-funds',
    isExact: true,
    navLinkId: customerDetailsNavIds.EQ_FUNDS,
    component: EqFundsContainer,
    componentProps: {
      history: props.history,
      customerId: props.selectedCustomerId,
      customerDetails: props.customerDetails,
      permissions: props.permissions,
    },
    permissions: ['EQ_FUNDS'],
  }),
  EQ_PNL: (props) => ({
    path: '/eq-pnl',
    isExact: true,
    navLinkId: customerDetailsNavIds.EQ_PNL,
    component: DefaultTemplate,
    componentProps: {
      history: props.history,
      customerId: props.selectedCustomerId,
      customerDetails: props.customerDetails,
    },
    permissions: ['EQ_PNL'],
  }),
  EQ_STATEMENTS_AND_FORMS: (props) => ({
    path: '/eq-statements-and-forms',
    isExact: true,
    navLinkId: customerDetailsNavIds.EQ_STATEMENTS_AND_FORMS,
    component: EqStatements,
    componentProps: {
      history: props.history,
      customerId: props.selectedCustomerId,
      customerDetails: props.customerDetails,
    },
    permissions: ['EQ_STATEMENT_FORM'],
  }),
  EQ_EPF_DETAILS: (props) => ({
    path: '/epf-details',
    isExact: true,
    navLinkId: customerDetailsNavIds.EQ_EPF_DETAILS,
    component: EpfComponent,
    componentProps: {
      history: props.history,
      customerId: props.selectedCustomerId,
      customerDetails: props.customerDetails,
    },
    permissions: ['EPF_DETAILS'],
  }),
  EQ_IPO_APPLICATIONS: (props) => ({
    path: '/eq-ipo-applications',
    isExact: true,
    navLinkId: customerDetailsNavIds.EQ_IPO_APPLICATIONS,
    component: CustomerIpoApplications,
    componentProps: {
      history: props.history,
      customerId: props.selectedCustomerId,
      customerDetails: props.customerDetails,
    },
    permissions: ['EQ_ORDERS'],
  }),
  EQ_HNI_IPO_APPLICATIONS: (props) => ({
    path: '/eq-ipo-hni-applications',
    isExact: true,
    navLinkId: customerDetailsNavIds.EQ_HNI_IPO_APPLICATIONS,
    component: CustomerIPOHniApplications,
    componentProps: {
      history: props.history,
      customerId: props.selectedCustomerId,
      customerDetails: props.customerDetails,
    },
    permissions: ['EQ_ORDERS'],
  }),
  EQ_SUBSCRIPTION: (props) => ({
    path: '/eq-subscription',
    isExact: true,
    navLinkId: customerDetailsNavIds.EQ_SUBSCRIPTION,
    component: EqSubsLayout,
    componentProps: {
      history: props.history,
      customerId: props.selectedCustomerId,
      customerDetails: props.customerDetails,
      agentId: props.userId,
    },
    permissions: ['SUBSCRIPTION_WRITE', 'SUBSCRIPTION_READ'],
  }),
  EQ_MARGIN_PLEDGE: (props) => ({
    path: '/eq-margin-pledge',
    isExact: true,
    navLinkId: customerDetailsNavIds.EQ_MARGIN_PLEDGE,
    component: EqMarginPledgeLayout,
    componentProps: {
      history: props.history,
      customerId: props.selectedCustomerId,
      customerDetails: props.customerDetails,
    },
    permissions: ['EQ_ORDERS'],
  }),
  EQ_BUYBACK: (props) => ({
    path: '/eq-buyback',
    isExact: true,
    navLinkId: customerDetailsNavIds.EQ_BUYBACK,
    component: EqBuybackLayout,
    componentProps: {
      history: props.history,
      customerId: props.selectedCustomerId,
    },
    permissions: ['EQ_ORDERS'],
  }),
  ACTIVE_CAMPAIGNS: (props) => ({
    path: '/active-campaigns',
    isExact: true,
    navLinkId: customerDetailsNavIds.ACTIVE_CAMPAIGNS,
    component: ActiveCampaigns,
    componentProps: {
      customerId: props.selectedCustomerId,
    },
    permissions: ['BASIC_DETAILS'],
  }),
};

export {
  CUSTOMER_DETAILS_ROUTES,
};
