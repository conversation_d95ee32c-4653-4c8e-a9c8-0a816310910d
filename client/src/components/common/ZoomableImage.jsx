import React from 'react';
import PropTypes from 'prop-types';
import ZoomableImage from '../utilities/ZoomableImage';

const ZoomableImg = ({
  alt,
  src,
  baseWidth,
  zoomWidth,
  zoomableWrapperId,
}) => (
  <ZoomableImage
    baseImage={{
      alt,
      src,
      width: baseWidth,
    }}
    largeImage={{
      alt,
      src,
      width: zoomWidth,
    }}
    thumbnailImage={{
      alt,
      src,
    }}
    mapScaleFactor={0.2}
    zoomableWrapperId={zoomableWrapperId}
  />
);

ZoomableImg.propTypes = {
  alt: PropTypes.string,
  src: PropTypes.string,
  baseWidth: PropTypes.number,
  zoomWidth: PropTypes.number,
  zoomableWrapperId: PropTypes.string,
};

ZoomableImg.defaultProps = {
  alt: '',
  src: '',
  baseWidth: 320,
  zoomWidth: 480,
  zoomableWrapperId: 'zoomable-wrapper',
};

export default ZoomableImg;
