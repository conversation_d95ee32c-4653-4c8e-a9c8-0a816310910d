import React from 'react';
import { Route } from 'react-router-dom';
import PropTypes from 'prop-types';
import ErrorBoundary from './ErrorBoundry';

const RouteComponent = ({ updateNavLinkFn, configObj, disableTabFlag }) => {
  const {
    navLinkId,
    subLinkId,
    component: Component,
    componentProps = {},
  } = configObj;

  componentProps.disableTabFlag = disableTabFlag;
  return (
    <Route
      render={() => {
        updateNavLinkFn(navLinkId, subLinkId);
        return (<ErrorBoundary><Component {...componentProps} /></ErrorBoundary>);
      }}
    />
  );
};

RouteComponent.propTypes = {
  updateNavLinkFn: PropTypes.func.isRequired,
  disableTabFlag: PropTypes.object,
  configObj: PropTypes.shape({
    component: PropTypes.any,
    navLinkId: PropTypes.number,
    subLinkId: PropTypes.number,
    componentProps: PropTypes.object,
  }).isRequired,
};

RouteComponent.defaultProps = {
  disableTabFlag: {},
};

export default RouteComponent;
