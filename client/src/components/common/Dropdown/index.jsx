import React from 'react';
import PropTypes from 'prop-types';

import './styles.scss';

function Dropdown({
  data = [], onItemClick, placeholder, disabled, value,
}) {
  return (
    <div className="select-dropdown">
      <select onChange={onItemClick} disabled={disabled} value={value}>
        <option value="" disabled selected hidden>{placeholder}</option>
        {data.map((item) => (
          <option key={item.id} className="select-dropdown__item" value={item.value}>{item.label}</option>
        ))}
      </select>

    </div>

  );
}

Dropdown.propTypes = {
  data: PropTypes.arrayOf(PropTypes.string),
  onItemClick: PropTypes.func,
  placeholder: PropTypes.string,
  disabled: PropTypes.bool,
  value: PropTypes.string,
};

Dropdown.defaultProps = {
  data: [],
  onItemClick: () => {},
  placeholder: 'Select',
  disabled: false,
  value: '',
};

export default Dropdown;
