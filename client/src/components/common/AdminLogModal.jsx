import React from 'react';
import Modal from 'react-modal';
import PropTypes from 'prop-types';
import { PtmButton } from '@paytm-money/frontend-common-v2';

import { convertUTCTimeToIST } from '../../util';
import '../../styles/generic-modal.css';

const AdminLogModal = ({ isLogModalOpen, onLogModalClose, logModalData }) => {
  const keysArr = Object.keys(logModalData);
  const colElements = keysArr.map((key) => {
    if (!logModalData[key]) {
      return null;
    }
    const val = key === 'timestamp' ? convertUTCTimeToIST(logModalData[key], 'DD-MM-YYYY hh:mm:ss A') : logModalData[key];
    return (
      <div className="col-xs-6 col-sm-6">
        <p className="remove-margin font-s12 text-gray-dark">{key}</p>
        <p className="remove-margin-b push-5-t h4 text-city-dark">{val}</p>
      </div>
    );
  });
  return (
    <Modal
      isOpen={isLogModalOpen}
      onRequestClose={onLogModalClose}
      overlayClassName={'md-overlay'}
      className={'md-content'}
      style={{ content: { width: '50%' } }}
      ariaHideApp={false}
    >
      <div className="block-content">
        <div className="row items-push">
          {colElements}
        </div>
        <PtmButton
          appearance="primaryDark"
          handlePress={onLogModalClose}
        >
          OKAY
        </PtmButton>
      </div>
    </Modal>
  );
};

AdminLogModal.propTypes = {
  isLogModalOpen: PropTypes.bool.isRequired,
  onLogModalClose: PropTypes.func.isRequired,
  logModalData: PropTypes.object.isRequired,
};

export default AdminLogModal;
