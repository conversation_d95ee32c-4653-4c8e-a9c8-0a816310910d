import React from 'react';
import PropTypes from 'prop-types';
import Tooltip from 'rc-tooltip';
import 'rc-tooltip/assets/bootstrap_white.css';
import Button from '../ActionButton';
import './ToolTipStyles.scss';

const ToolTip = (props) => {
  const {
    placement,
    tooltipContent,
    onTooltipVisibilityChange,
    onButtonClickAction,
    tooltipChildStyles,
  } = props;
  return (
    <Tooltip
      placement={placement}
      overlay={tooltipContent}
      overlayClassName="info-tooltip"
      onVisibleChange={onTooltipVisibilityChange}
    >
      <span style={tooltipChildStyles}>
        <Button.IconButton
          buttonStyle="info-button"
          iconClass="fa fa-info-circle info-icon"
          onClickAction={onButtonClickAction}
        />
      </span>
    </Tooltip>
  );
};

ToolTip.propTypes = {
  placement: PropTypes.string,
  tooltipContent: PropTypes.oneOfType([
    PropTypes.string || PropTypes.node,
  ]).isRequired,
  onTooltipVisibilityChange: PropTypes.func,
  onButtonClickAction: PropTypes.func,
  tooltipChildStyles: PropTypes.object,
};

ToolTip.defaultProps = {
  placement: 'top',
  onButtonClickAction: () => {},
  tooltipChildStyles: {
    display: 'table-cell',
    textAlign: 'center',
    verticalAlign: 'middle',
  },
  onTooltipVisibilityChange: () => {},
};

export default ToolTip;
