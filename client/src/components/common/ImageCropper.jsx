import React from 'react';
import ImageSelector from '../utilities/image-selector/ImageSelectorContainer';

function onCrop(selectedAreaOriginalSize, img) {
  const {
    x, y, width, height,
  } = selectedAreaOriginalSize;
  // ------ Create canvas to draw the cropped image ---------//
  const canvas = document.createElement('canvas');
  canvas.width = width;
  canvas.height = height;
  const ctx = canvas.getContext('2d');
  // Crop and draw image of the original size using updated coordinates
  ctx.drawImage(img, x, y, width, height, 0, 0, width, height);
  // ----------------------------------------------------//

  return canvas;
}

const ImageCropper = props => (
  <ImageSelector
    {...props}
    buttonLabel="Crop"
    createCanvas={onCrop}
  />
);

export default ImageCropper;
