import React, { useRef, useState, useEffect } from 'react';

import './styles.scss';

const NavigationScroll = (props) => {
  const {
    configArray,
    selectedTabId,
    navComponent,
    width,
    ...rest
  } = props;

  const containerRef = useRef(null);

  const NavComponent = navComponent;

  const [scrollPos, setScrollPos] = useState(0);
  const [clientWidth, setClientWidth] = useState(null);
  const [scrollWidth, setScrollWidth] = useState(null);

  useEffect(() => {
    const scrollElem = document.getElementById('navLinkScroll');
    if (scrollElem) {
      setClientWidth(scrollElem.clientWidth);
      setScrollWidth(scrollElem.scrollWidth);
    }
    window.addEventListener('resize', adjustScroll);
  }, [configArray]);

  const adjustScroll = () => {
    const scrollElem = document.getElementById('navLinkScroll');
    if (scrollElem) {
      setClientWidth(scrollElem.clientWidth);
      setScrollWidth(scrollElem.scrollWidth);
    }
  };

  const scrollElement = (type) => {
    const scrollElem = document.getElementById('navLinkScroll');
    const newPos = type === 'right' ? scrollPos - width : scrollPos - width;
    if (type === 'right') {
      scrollElem.scrollLeft += width;
    } else {
      scrollElem.scrollLeft -= width;
    }
    setScrollPos(newPos);
  };

  const navigationTabs = configArray.map((navObj, index) => {
    const styles = {
      minWidth: width,
      maxWidth: width,
    };

    const { isDisabled } = navObj;

    return (
      <div
        key={`${navObj}_${index}`}
        className={`flex-container padding-0 nav-tab
        ${isDisabled ? 'disable-btn' : ''}`}
        style={styles}
      >
        <NavComponent
          width={width}
          navObj={navObj}
          isSelected={navObj.id === selectedTabId}
          {...rest}
        />
      </div>
    );
  });

  const leftScroll = () => {
    scrollElement('left');
  };

  const rightScroll = () => {
    scrollElement('right');
  };

  const navButtons = () => {
    if (scrollWidth > clientWidth) {
      const scrollElem = document.getElementById('navLinkScroll');
      const isrightScrollable = clientWidth + scrollElem.scrollLeft < scrollWidth ? 'active' : '';
      const isleftScrollable = scrollElem.scrollLeft ? 'active' : '';

      return (
        <div className="flex-container nav-btns">
          <i onClick={leftScroll} className={`fa fa-chevron-left arrow ${isleftScrollable}`} />
          <i onClick={rightScroll} className={`fa fa-chevron-right arrow ${isrightScrollable}`} />
        </div>
      );
    }
  };

  return (
    <div className="flex-container padding-0 nav-link-scroll">
      <div className="flex-container nav-link-container" ref={containerRef} id="navLinkScroll">
        {navigationTabs}
      </div>
      {navButtons()}
    </div>
  );
};

export default NavigationScroll;
