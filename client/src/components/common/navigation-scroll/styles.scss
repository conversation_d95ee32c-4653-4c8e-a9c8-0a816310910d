.nav-link-scroll{
    background: #697078;
    .nav-link-container{
        justify-content: flex-start;
        padding: 0px;
        overflow-x: hidden;
        position: relative;
        height: 48px;
        .nav-tab{
          a{
              width: 100%;
              height: 100%;
              text-align: center;
              .block-content{
                  font-weight: 400;
                  font-size: 14px;
                  color: #FFFFFF;
                  opacity: .7;
              }
              &.block{
                  background: #697078;
                  color: #FFFFFF;
              }
              .selected-tab{
                color: #697078;
                background: #FFFFFF;
                opacity: 1;
                border-radius: 4px 4px 0px 0px;
                span{
                    color: #697078;
                    font-weight: 600;
                }
              }
          }
        }
    }
    .nav-btns{
        width: 40px;
        z-index: 99;
        .arrow{
            font-size: 18px;
            padding: 5px;
            opacity: .5;
            cursor: pointer;
            &.active{
                opacity: 1;
            }
        }
    }
}
