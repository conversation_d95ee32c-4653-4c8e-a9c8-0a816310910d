import React, { Component } from 'react';
import autobind from 'autobind-decorator';
import PropTypes from 'prop-types';
import debounce from 'lodash/debounce';

import Button from '../ActionButton';
import Loader from '../Loader';

@autobind
export default class SearchBar extends Component {
  constructor(props) {
    super(props);
    this.state = {
      value: '' || this.props.initialValue,
      isFocused: false,
    };

    this.debouncedChange = debounce(
      this.handleDebouncedChange,
      props.delay,
    );
  }

  componentDidMount() {
    if (this.props.autofocus) {
      this.input.focus();
    }
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.shouldClearValueOnSearch
      && this.props.shouldShowLoader
      && !nextProps.shouldShowLoader
      && nextProps.isRequestSuccess) {
      this.setState({
        value: '',
      });
    }
  }

  handleDebouncedChange(searchTerm) {
    this.props.onChange(searchTerm);
  }

  handleKeyDown(event) {
    switch (event.key) {
      case 'ArrowUp':
      case 'ArrowDown':
        event.preventDefault();
        this.props.handleArrowKeys(event.key === 'ArrowDown');
        break;

      case 'Enter':
        this.handleSearch();
        break;

      case 'Escape':
        this.props.handleEscape();
        break;

      default:
    }
  }

  handleChange(event) {
    const { value } = event.target;
    const searchTerm = value.toLowerCase().trim();
    this.setState({
      value,
    });

    if (this.props.delay > 0) {
      this.debouncedChange(searchTerm);
    } else {
      this.props.onChange(searchTerm);
    }
  }

  handleSearch() {
    this.props.onSearch(this.state.value.trim());
  }

  clearSearch() {
    this.setState({
      value: '',
    });

    this.input.focus();
  }


  toggleFocus() {
    const { isFocused } = this.state;
    this.setState({ isFocused: !isFocused });
  }

  render() {
    const {
      renderSearchButton,
      placeholderText,
      renderClearButton,
      parentDivClass,
      inputWrapperClass,
      inputFieldClass,
      submitBtnClass,
      id,
      shouldShowLoader,
      disabled,
      type,
    } = this.props;
    const displayClearButton = this.state.value && renderClearButton;

    const classObj = {
      parent: `${parentDivClass} input-group`,
      inputWrap: `${inputWrapperClass} input-group input-group-lg`,
      input: `${inputFieldClass} form-control`,
      submitButton: `${submitBtnClass} btn btn-default`,
    };

    return (
      <div ref={(ref) => { this.container = ref; }} className={classObj.parent}>
        <div className={classObj.inputWrap}>
          <input
            type={type}
            id={id}
            className={classObj.input}
            ref={(ref) => { this.input = ref; }}
            value={this.state.value}
            onChange={this.handleChange}
            onFocus={this.toggleFocus}
            onBlur={this.toggleFocus}
            onKeyDown={this.handleKeyDown}
            placeholder={placeholderText}
            disabled={disabled}
            style={{ height: '28px', paddingLeft: '25px' }}
          />

          {shouldShowLoader && (
            <div style={{ position: 'relative', padding: '20px 0 0 5px' }}>
              <Loader.SearchLoader />
            </div>
          )}

          { displayClearButton && (
            <div style={{
              position: 'relative',
              marginTop: -30,
              float: 'right',
              marginRight: 5,
              zIndex: 5,
            }}
            >
              <Button.IconButton
                buttonStyle=""
                iconClass="fa fa-remove text-light"
                onClickAction={this.clearSearch}
              />
            </div>
          )}

          {renderSearchButton && (
            <div className="input-group-btn ">
              <Button.IconButton
                buttonStyle={`${classObj.submitButton} fixHeight`}
                iconClass="fa fa-search text-light"
                onClickAction={() => this.props.onSearch(this.state.value.trim())}
              />
            </div>
          )}
        </div>
      </div>
    );
  }
}

SearchBar.propTypes = {
  onSearch: PropTypes.func.isRequired,
  delay: PropTypes.number,
  autofocus: PropTypes.bool,
  onChange: PropTypes.func,
  renderSearchButton: PropTypes.bool,
  placeholderText: PropTypes.string,
  handleArrowKeys: PropTypes.func,
  renderClearButton: PropTypes.bool,
  parentDivClass: PropTypes.string,
  inputWrapperClass: PropTypes.string,
  inputFieldClass: PropTypes.string,
  submitBtnClass: PropTypes.string,
  id: PropTypes.string,
  handleEscape: PropTypes.func,
  shouldClearValueOnSearch: PropTypes.bool,
  shouldShowLoader: PropTypes.bool,
  isRequestSuccess: PropTypes.bool,
  initialValue: PropTypes.string,
  type: PropTypes.string,
  disabled: PropTypes.bool,
};

SearchBar.defaultProps = {
  parentDivClass: 'form-material-primary',
  inputWrapperClass: '',
  inputFieldClass: '',
  submitBtnClass: '',
  delay: 0,
  autofocus: false,
  onChange: () => undefined,
  handleArrowKeys: () => undefined,
  renderSearchButton: true,
  placeholderText: 'Search...',
  renderClearButton: false,
  id: '',
  handleEscape: () => undefined,
  shouldClearValueOnSearch: false,
  shouldShowLoader: false,
  isRequestSuccess: false,
  initialValue: '',
  type: 'text',
  disabled: false,
};
