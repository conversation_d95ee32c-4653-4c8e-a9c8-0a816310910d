import React from 'react';
import PropTypes from 'prop-types';

export default class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    try {
      console.log(error, errorInfo);
    } catch (err) {

    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="app-error-boundary">
          <p>Something went wrong.</p>
          <a role="presentation" onClick={() => window.location.reload()} style={{ fontSize: '16px', cursor: 'pointer' }}>Try again</a>
        </div>
      );
    }

    return this.props.children;
  }
}

ErrorBoundary.propTypes = {
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
  ]).isRequired,
};
