import React from 'react';
import PropTypes from 'prop-types';
import '../../styles/loaders.css';

const Loader = {
  SearchLoader: () => (
    <div className="search-loader" />
  ),
  PageLoader: () => (
    <div id="page-loader" />
  ),
  ContentLoader: params => (
    <main id="main-container" className={`section-ldr-wrpr ${params.containerClass}`}>
      <div className="ajax-loader" />
    </main>
  ),
  SectionLoader: () => (
    <div className="section-loader" />
  ),
  AjaxLoader: () => (
    <div className="ajax-loader" />
  ),
  BtnGroupLoader: ({ style }) => (
    <div
      style={{
        display: 'inline-block',
        float: 'left',
        position: 'relative',
        padding: 10,
        marginLeft: 5,
        marginTop: -2,
        ...style,
      }}
    >
      <Loader.SearchLoader />
    </div>
  ),
};

Loader.BtnGroupLoader.propTypes = {
  style: PropTypes.object,
};

Loader.BtnGroupLoader.defaultProps = {
  style: {},
};

Loader.ContentLoader.propTypes = {
  params: PropTypes.object,
};

Loader.ContentLoader.defaultProps = {
  params: {},
};
export default Loader;
