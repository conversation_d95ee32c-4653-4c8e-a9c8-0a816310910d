import React from 'react';
import PropTypes from 'prop-types';

const InformationCard = {
  TicketInfo: props => {
    const { label, value, priorityClass, containerClass } = props;
    const ticketIconClass = `fa fa-ticket fa-2x ${priorityClass}`;
    const valueTextClass = `h2 ${priorityClass}`;
    return (
      <div className={`${containerClass}`}>
        <div className="block block-link-hover1">
          <div className="block-content block-content-full clearfix">
            <div className="pull-right push-15-t push-15">
              <i className={ticketIconClass} />
            </div>
            <div className={valueTextClass}>{value}</div>
            <div className="text-uppercase font-w600 font-s10 text-muted">
              {label}
            </div>
          </div>
        </div>
      </div>
    );
  },

  SummaryInfo: props => {
    const { isAbsoluteAmount, textClass, label, value } = props;
    const textComponent = isAbsoluteAmount ? (
      <div className={`h4 font-w600 ${textClass}`}>{value}</div>
    ) : (
      <div className={`h4 font-w600 ${textClass}`}>{`${value} %`}</div>
    );
    return (
      <div className="col-sm-6 col-lg-3">
        <div className="block block-rounded block-link-hover3 text-center">
          <div className="block-content block-content-full">
            {textComponent}
            <div className="h5 text-muted push-5-t">{label}</div>
          </div>
        </div>
      </div>
    );
  },

  ListItemCard: props => {
    const { list, iconClass, headerClass, heading } = props;

    return (
      <div className="col-sm-6 col-lg-3">
        <a className="block block-link-hover1 text-center">
          <div
            className={`block-content block-content-full ${headerClass}`}
            style={{
              display: 'flex',
              alignItems: 'baseline',
              justifyContent: 'center'
            }}
          >
            <i
              className={`${iconClass} fa-lg text-white`}
              style={{ paddingRight: 3 }}
            />
            <span className="text-white h4">{heading}</span>
          </div>
          <div className="block-content block-content-full block-content-mini">
            <ul className="text-left remove-padding-l push-20-l push-10-t">
              {list}
            </ul>
          </div>
        </a>
      </div>
    );
  }
};

InformationCard.TicketInfo.propTypes = {
  label: PropTypes.string.isRequired,
  value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]).isRequired,
  priorityClass: PropTypes.string.isRequired,
  containerClass: PropTypes.string
};

InformationCard.TicketInfo.defaultProps = {
  containerClass: 'col-xs-6 col-lg-3 '
};

InformationCard.SummaryInfo.propTypes = {
  isAbsoluteAmount: PropTypes.bool.isRequired,
  textClass: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  value: PropTypes.string.isRequired
};

InformationCard.ListItemCard.propTypes = {
  list: PropTypes.array.isRequired,
  iconClass: PropTypes.string,
  headerClass: PropTypes.string,
  heading: PropTypes.oneOfType([PropTypes.string, PropTypes.node])
};

InformationCard.ListItemCard.defaultProps = {
  heading: '',
  iconClass: '',
  headerClass: ''
};

export default InformationCard;
