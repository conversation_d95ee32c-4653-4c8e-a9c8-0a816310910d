import React from 'react';
import PropTypes from 'prop-types';

import Loader from './Loader';
import Error from './Error';

function PageLoadingHOC(WrappedComponent) {
  function HOC({ loading, error, ...rest }) {
    if (error) {
      return <Error errorMsg={error} />;
    }

    if (loading) {
      return <Loader.ContentLoader />;
    }

    return (
      <WrappedComponent {...rest} />
    );
  }

  HOC.propTypes = {
    loading: PropTypes.bool.isRequired,
    error: PropTypes.string,
  };

  HOC.defaultProps = {
    error: null,
  };

  return HOC;
}

export default PageLoadingHOC;
