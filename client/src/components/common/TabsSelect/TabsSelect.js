import React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';

import './tabs-select.scss';

const TabsSelect = (props) => {
  const styles = {
    container: 'tabs-select__container',
    tabButton: 'tabs-select__tab-button',
    tabButtonSelected: 'tabs-select__tab-button-selected',
  };

  return (
    <div className={styles.container}>
      {props.tabsConfig.map((tab) => (
        <button
          type="button"
          onClick={() => props.handleTabClick(tab)}
          className={classNames(styles.tabButton, {
            [styles.tabButtonSelected]: tab.id === props.selectedTabId,
          })}
        >
          {tab.tabName}
        </button>
      ))}
    </div>
  );
};

TabsSelect.propTypes = {
  selectedTabId: PropTypes.number.isRequired,
  tabsConfig: PropTypes.array.isRequired,
  handleTabClick: PropTypes.func.isRequired,
};

export default TabsSelect;
