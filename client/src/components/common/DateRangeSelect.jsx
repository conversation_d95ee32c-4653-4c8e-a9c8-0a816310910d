import React from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import DateTime from 'react-datetime';
import 'react-datetime/css/react-datetime.css';

import '../../styles/date-range-select.css';

function onDatesChange(dateFormat, from, to, updateChange) {
  const fromMoment = moment(from, dateFormat);
  const toMoment = moment(to, dateFormat);
  const updatedDates = {
    from: fromMoment.isValid() ? fromMoment.format(dateFormat) : '',
    to: toMoment.isValid() ? toMoment.format(dateFormat) : '',
  };
  if (fromMoment.isValid() && fromMoment.isAfter(toMoment)) {
    updatedDates.to = '';
  }
  updateChange(updatedDates);
}

function onMonthChange(dateFormat, month, updateChange) {
  const monthMoment = moment(month, dateFormat);
  const updateMonth = {
    month: monthMoment.isValid() ? monthMoment.format(dateFormat) : '',
  };
  updateChange(updateMonth);
}

function isToDateValid(date, from, dateFormat, disableFutureToDates) {
  const fromMoment = moment(from, dateFormat);
  if (disableFutureToDates) {
    const today = moment();
    return date.isBefore(today);
  }
  if (!fromMoment.isValid()) {
    return true;
  }
  return date.isSameOrAfter(fromMoment);
}

function isFromDateValid(date, futureDatesAllowed) {
  if (futureDatesAllowed) {
    return true;
  }
  const today = moment();
  return date.isBefore(today);
}

function bspaceDelPressed(event) {
  const key = event.which || event.keyCode;
  return key === 8 || key === 46;
}

function handleFromKeydown(event, to, updateChange) {
  if (bspaceDelPressed(event)) {
    updateChange({
      from: '',
      to,
    });
  }
  event.preventDefault();
  return false;
}

function handleToKeydown(event, from, updateChange) {
  if (bspaceDelPressed(event)) {
    updateChange({
      from,
      to: '',
    });
  }
  event.preventDefault();
  return false;
}

const DateRangeSelect = ({
  dateFormat,
  from,
  to,
  month,
  disabled,
  onChange,
  futureDatesAllowed,
  disableFutureToDates,
}) => {
  if (dateFormat === 'MM-YYYY') {
    return (
      <div className="input-daterange input-group">
        <DateTime
          value={month}
          dateFormat={dateFormat}
          timeFormat={null}
          inputProps={{
            disabled,
            placeholder: 'Month, Year',
          }}
          onChange={
          (newMonth) => onMonthChange(dateFormat, newMonth, onChange)
        }
        />
      </div>
    );
  }

  return (
    <div className="input-daterange input-group">
      <DateTime
        value={from}
        dateFormat={dateFormat}
        timeFormat={null}
        inputProps={{
          disabled,
          placeholder: 'From',
          onKeyDown: (e) => handleFromKeydown(e, to, onChange),
        }}
        onChange={
        (newFrom) => onDatesChange(dateFormat, newFrom, to, onChange)
      }
        isValidDate={(date) => isFromDateValid(date, futureDatesAllowed)}
      />
      <span className="input-group-addon">
        <i className="fa fa-chevron-right" />
      </span>
      <DateTime
        value={to}
        dateFormat={dateFormat}
        timeFormat={null}
        inputProps={{
          disabled,
          placeholder: 'To',
          onKeyDown: (e) => handleToKeydown(e, from, onChange),
        }}
        isValidDate={(date) => isToDateValid(date, from, dateFormat, disableFutureToDates)}
        onChange={
        (newTo) => onDatesChange(dateFormat, from, newTo, onChange)
      }
      />
    </div>
  );
};

DateRangeSelect.propTypes = {
  dateFormat: PropTypes.string,
  from: PropTypes.string,
  to: PropTypes.string,
  month: PropTypes.string,
  disabled: PropTypes.bool,
  onChange: PropTypes.func.isRequired,
  futureDatesAllowed: PropTypes.bool,
  disableFutureToDates: PropTypes.bool,
};

DateRangeSelect.defaultProps = {
  disabled: false,
  dateFormat: 'DD/MM/YYYY',
  from: '',
  to: '',
  month: '',
  futureDatesAllowed: true,
  disableFutureToDates: false,
};

export default DateRangeSelect;
