import React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';

import '../../styles/accordian.css';

const Accordian = (props) => {
  const {
    label,
    isOpen,
    onClick,
    children,
    containerClass,
  } = props;

  const containerClassName = classNames(containerClass || 'accordian-container', 'col-sm-12', {
    'accordian-open-container': isOpen,
    'accordian-closed-container': !isOpen,
  });

  return (
    <div className={containerClassName}>
      <div onClick={onClick} role="button">
        {label}
        <div className="pull-right">
          {isOpen && <span>&#9650;</span>}
          {!isOpen && <span>&#9660;</span>}
        </div>
      </div>
      {isOpen && (
        <div className="accordian-content">
          {children}
        </div>
      )}
    </div>
  );
};

Accordian.propTypes = {
  isOpen: PropTypes.bool,
  onClick: PropTypes.func.isRequired,
  children: PropTypes.node.isRequired,
  label: PropTypes.oneOfType([PropTypes.string, PropTypes.node]).isRequired,
  containerClass: PropTypes.string,
};

Accordian.defaultProps = {
  containerClass: '',
  isOpen: false,
};

export default Accordian;
