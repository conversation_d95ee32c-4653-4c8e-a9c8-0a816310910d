import React from 'react';
import PropTypes from 'prop-types';
import Loader from './Loader';
import '../../styles/action-buttons.css';

const Button = {
  IconButton: (props) => {
    const {
      iconClass,
      onClickAction,
      title,
      buttonStyle,
    } = props;
    return (
      <button
        className={buttonStyle}
        type="button"
        onClick={onClickAction}
        title={title}
      >
        <i className={iconClass} aria-hidden="true" />
      </button>
    );
  },

  IconButtonGroup: (props) => {
    const { iconClassArr, onClickActionArr } = props;
    return (
      <div className="btn-group" data-toggle="buttons">
        {
          iconClassArr.map((iconClass, index) => (
            <Button.IconButton
              key={index}
              iconClass={iconClass}
              onClickAction={onClickActionArr[index]}
            />
          ))
        }
      </div>
    );
  },

  TextButton: (props) => {
    const {
      name,
      isDisabled,
      label,
      onPress,
      containerClass,
      buttonClass,
      buttonType,
      showLoader,
      ...buttonOptns
    } = props;
    return (
      <div className={containerClass}>
        <button
          name={name}
          disabled={isDisabled}
          className={buttonClass}
          type={buttonType}
          onClick={onPress}
          {...buttonOptns}
          // id="button"
        >
          {label}
          { showLoader && (
            <Loader.SearchLoader />
          )}
        </button>
      </div>
    );
  },
};

Button.IconButtonGroup.propTypes = {
  iconClassArr: PropTypes.array.isRequired, // eslint-disable-line react/forbid-prop-types
  onClickActionArr: PropTypes.array.isRequired, // eslint-disable-line react/forbid-prop-types
};

Button.IconButton.propTypes = {
  iconClass: PropTypes.string.isRequired,
  onClickAction: PropTypes.func.isRequired,
  title: PropTypes.string,
  buttonStyle: PropTypes.string,
};

Button.IconButton.defaultProps = {
  title: '',
  buttonStyle: 'btn btn-default btn-primary-dark',
};

Button.TextButton.propTypes = {
  id: PropTypes.string,
  isDisabled: PropTypes.bool,
  label: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.node,
  ]),
  onPress: PropTypes.func,
  containerClass: PropTypes.string,
  buttonClass: PropTypes.string,
  buttonType: PropTypes.string,
  showLoader: PropTypes.bool,
  name: PropTypes.string,
};

Button.TextButton.defaultProps = {
  isDisabled: false,
  containerClass: 'col-xs-6 col-sm-2 text-center',
  buttonClass: 'btn btn-minw btn-square btn-primary-dark push-10-t',
  onPress: () => {},
  label: 'Take Action',
  buttonType: 'button',
  showLoader: false,
  name: '',
  id: '',
};

export default Button;
