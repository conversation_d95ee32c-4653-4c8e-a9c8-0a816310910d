import React from 'react';
import ImageSelector from '../utilities/image-selector/ImageSelectorContainer';

function onBlur(selectedAreaOriginalSize, img) {
  const {
    x, y, width, height,
  } = selectedAreaOriginalSize;

  // ------ Create canvas and draw image with the
  // selected rectangle filled with black color ---------//
  const canvas = document.createElement('canvas');
  canvas.width = img.naturalWidth;
  canvas.height = img.naturalHeight;
  const ctx = canvas.getContext('2d');
  // Draw the image of the original size and black out the selected area
  ctx.drawImage(img, 0, 0);
  ctx.fillRect(x, y, width, height);
  // ----------------------------------------------------//

  return canvas;
}

const ImageBlurEditor = props => (
  <ImageSelector
    {...props}
    buttonLabel="Blur"
    createCanvas={onBlur}
  />
);

export default ImageBlurEditor;
