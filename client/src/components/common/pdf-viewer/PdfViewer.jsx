import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { PtmSelect, PtmButton } from '@paytm-money/frontend-common-v2';
import { Document, Page } from 'react-pdf/dist/esm/entry.webpack';

function PdfViewer({
  url, width, withCredentials, secondaryCtaText,
}) {
  const [numPages, setNumPages] = useState(null);
  const [showView, setView] = useState(false);
  const [pageNumberOption, setPageNumberOption] = useState({ label: 1, value: 1 });

  function onDocumentLoadSuccess({ numPages: pages }) {
    setNumPages(pages);
  }

  function getOptions(pages) {
    const options = [];
    for (let i = 1; i <= pages; i += 1) {
      options.push({ label: i, value: i });
    }
    return options;
  }

  const onButtonClick = () => {
    const alink = document.createElement('a');
    alink.target = '_blank';
    alink.href = url;
    alink.click();
  };

  const pageOptions = getOptions(numPages);

  return (
    <div className="pdf-viewer">
      {!showView
        ? (
          <div>
            <PtmButton
              appearance="primaryDark"
              handlePress={() => { setView(true); }}
            >
              {' '}
              View PDF
            </PtmButton>
            {' '}
            <PtmButton
              appearance="primaryDark"
              handlePress={() => { onButtonClick(); }}
            >
              {secondaryCtaText}
            </PtmButton>
          </div>
        )
        : (
          <div>
            <div style={{ width: `${width}px` }}>
              <Document
                file={{ url, withCredentials }}
                onLoadSuccess={onDocumentLoadSuccess}
                renderMode="canvas"
              >
                <Page
                  width={width}
                  pageNumber={pageNumberOption.value}
                  renderAnnotationLayer={false}
                  renderTextLayer={false}
                />
                <div>
                  <p>Select Page</p>
                  <PtmSelect
                    options={pageOptions}
                    value={pageNumberOption}
                    onChange={setPageNumberOption}
                  />
                </div>
              </Document>
            </div>
            <div>
              <PtmButton
                appearance="primaryDark"
                handlePress={() => { onButtonClick(); }}
              >
                {secondaryCtaText}
              </PtmButton>
            </div>
          </div>
        )}
    </div>
  );
}

PdfViewer.propTypes = {
  url: PropTypes.string.isRequired,
  width: PropTypes.number,
  withCredentials: PropTypes.bool,
  secondaryCtaText: PropTypes.string,
};

PdfViewer.defaultProps = {
  width: 400,
  withCredentials: true,
  secondaryCtaText: 'View PDF in new Tab',
};

export default PdfViewer;
