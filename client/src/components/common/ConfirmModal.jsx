import React from 'react';
import PropTypes from 'prop-types';
import { PtmButton } from '@paytm-money/frontend-common-v2';

import GenericModal from './GenericModal';

const customStyles = {
  content: {
    height: '150px',
    inset: 'unset !important',
    width: '400px',
  },
  overlay: {
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, .5)',
    display: 'flex',
    justifyContent: 'center',
  },
};
const ConfirmModal = (props) => {
  const {
    caption, onConfirm, modalProps, onRequestClose,
  } = props;
  return (
    <GenericModal
      isOpen={modalProps.isOpen}
      onClose={onRequestClose}
      style={customStyles}
    >
      <div className="confirm-modal" style={{ textAlign: 'center' }}>
        <p>{caption}</p>
        <div>
          <PtmButton
            buttonInlineStyle={{ marginRight: 10 }}
            appearance="primary"
            handlePress={() => onConfirm(true)}
          >
            Yes
          </PtmButton>
          <PtmButton
            appearance="secondary"
            handlePress={() => onConfirm(false)}
          >
            No
          </PtmButton>
        </div>
      </div>
    </GenericModal>
  );
};

ConfirmModal.propTypes = {
  caption: PropTypes.string,
  onConfirm: PropTypes.func.isRequired,
  onRequestClose: PropTypes.func.isRequired,
  modalProps: PropTypes.func.isRequired,
};

ConfirmModal.defaultProps = {
  caption: 'Confirm',
};

export default ConfirmModal;
