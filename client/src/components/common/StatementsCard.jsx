import React from 'react';
import PropTypes from 'prop-types';
import Select from 'react-select';
import DateTime from 'react-datetime';
import moment from 'moment';
import { isEmpty } from 'lodash';
import DateRangeSelect from './DateRangeSelect';
import Button from './ActionButton';

const COMPONENT = {
  SELECT_BOX: 'SELECT_BOX',
  BUTTON: 'BUTTON',
  DATE_RANGE: 'DATE_RANGE',
  SELECT_DATE: 'SELECT_DATE',
  MONTH_YEAR: 'MONTH_YEAR',
  SELECT_DATE_WITH_SEGMENT: 'SELECT_DATE_WITH_SEGMENT',
  CUSTOM_TILL_DATE_SELECTION: 'CUSTOM_TILL_DATE_SELECTION',
};

function onDateChange(date, dateFormat, index, updateChange) {
  const dateMoment = moment(date, dateFormat);
  const updatedDate = {
    dateValue: dateMoment.isValid() ? dateMoment.format(dateFormat) : '',
  };
  updateChange(index, updatedDate);
}

function isCustomDateValid(date, irReadinessDate, maxDateAllowed, dateFormat) {
  const dateMoment = moment(date, dateFormat);
  const irReadinessDateMoment = moment(irReadinessDate, dateFormat);
  const maxDateAllowedMoment = moment(maxDateAllowed, dateFormat);
  if (dateMoment.isValid() && irReadinessDateMoment.isValid() && maxDateAllowedMoment.isValid()) {
    return dateMoment.isSameOrBefore(maxDateAllowedMoment)
      && dateMoment.isSameOrAfter(irReadinessDateMoment);
  }
  return false;
}

export default function Statements(props) {
  const {
    index,
    statementName,
    statements,
    handleRangeChange,
    DATE_RANGE_OPTIONS,
    buttonValues,
    buttonsList,
    updateMethods,
  } = props;

  const {
    updateFYValue,
    updateDateRange,
    updateMonth,
    updateSelectedDate,
    updateSegmentValue,
    updateCustomTillDate,
  } = updateMethods;

  function renderComponets(type, filterObj = null) {
    let filterOptions;
    switch (type) {
      case COMPONENT.SELECT_BOX:
        filterOptions = filterObj.options.map((optionsObj) => ({
          value: optionsObj.id,
          label: optionsObj.displayName,
        }));
        let defaultOption = {};
        defaultOption = { value: (filterObj.options.find((el) => el.lastFinYear === true) || {}).id, label: (filterObj.options.find((el) => el.lastFinYear === true) || {}).displayName };
        return (
          <Select
            isClearable
            defaultValue={defaultOption}
            className="remove-padding col-lg-8 push-30"
            classNamePrefix="fin-select"
            name="txn-statement"
            options={filterOptions}
            onChange={(val) => updateFYValue(index, val)}
          />
        );
      case COMPONENT.BUTTON:
        return (
          <Button.TextButton
            onPress={() => filterObj.requestStatement(index)}
            buttonClass="btn btn-minw btn-square btn-primary-dark btn-with-loader"
            containerClass="col-sm-6 text-center"
            label={filterObj.buttonLabel}
            disabled={filterObj.disableButton}
            showLoader={filterObj.selectedId === statements[index].id && filterObj.disableButton}
          />
        );
      case COMPONENT.DATE_RANGE:
        const { fromDate, toDate } = statements[index].dateRange;
        return (
          <span className="statement-cycle">
            <div className="title">{filterObj.displayName}</div>
            <DateRangeSelect
              dateFormat="DD/MM/YYYY"
              from={fromDate}
              to={toDate}
              onChange={(dateRange) => updateDateRange(index, dateRange)}
            />
          </span>
        );
      case COMPONENT.MONTH_YEAR:
        const { monthValue, yearValue } = statements[index].monthYearValue;
        return (
          <span className="statement-cycle">
            <div className="title">{filterObj.displayName}</div>
            <DateRangeSelect
              dateFormat="MM-YYYY"
              month={`${monthValue}-${yearValue}`}
              onChange={(month) => updateMonth(index, month)}
            />
          </span>
        );
      case COMPONENT.SELECT_DATE:
        const { selectedDate } = statements[index].dateRange;
        return (
          <span className="statement-cycle">
            <div className="title">{filterObj.displayName}</div>
            <DateTime
              dateFormat="DD-MM-YYYY"
              timeFormat={false}
              value={selectedDate}
              inputProps={{
                placeholder: 'Select Date',
              }}
              onChange={(dateValue) => onDateChange(dateValue, 'DD-MM-YYYY', index, updateSelectedDate)}
            />
          </span>
        );
      case COMPONENT.CUSTOM_TILL_DATE_SELECTION:
        const { fromDate: irReadinessDate, toDate: customTillDateMax, customTillDate } = statements[index].dateRange;
        return (
          <span className="statement-cycle">
            <div className="title">{filterObj.displayName}</div>
            <DateTime
              dateFormat="DD-MM-YYYY"
              timeFormat={false}
              value={customTillDate}
              inputProps={{
                placeholder: 'Select Date',
                onKeyDown: (e) => e.preventDefault(),
              }}
              onChange={(dateValue) => onDateChange(dateValue, 'DD/MM/YYYY', index, updateCustomTillDate)}
              isValidDate={(date) => isCustomDateValid(date, irReadinessDate, customTillDateMax, 'DD/MM/YYYY')}
            />
          </span>
        );
      case COMPONENT.SELECT_SEGMENT:
        const { selectedSegment, options } = statements[index].segmentOpt;
        return (
          <span className="statement-cycle">
            <Select
              isClearable
              className="remove-padding col-lg-8 push-30"
              name="txn-statement"
              defaultValue={selectedSegment}
              options={options}
              onChange={(val) => updateSegmentValue(index, val)}
            />
          </span>
        );
      default:
        break;
    }
  }


  function renderStatementRange() {
    const { filters } = statements[index];
    const {
      FY,
      TILL_DATE, DATE_RANGE,
      MONTH_YEAR,
      SELECT_DATE,
      SELECT_SEGMENT,
      SELECT_DATE_WITH_SEGMENT,
      LAST_30_DAYS,
      LAST_THREE_MONTH,
      LAST_SIX_MONTH,
      CUSTOM_TILL_DATE,
    } = DATE_RANGE_OPTIONS;
    return filters.map((filterObj, i) => {
      switch (filterObj.type) {
        case FY:
          return (
            <div key={i}>
              <label htmlFor="fy-statment">
                <input
                  id="fy-statment"
                  type="radio"
                  value={FY}
                  name={statements[index].id}
                  checked={statements[index].activeRangeType === FY}
                  onChange={(e) => handleRangeChange(index, e.target.value)}
                />
                <span className="statement-cycle">
                  <div className="title">{filterObj.displayName}</div>
                </span>
              </label>
              {renderComponets(COMPONENT.SELECT_BOX, filterObj)}
            </div>
          );
        case LAST_30_DAYS:
          return (
            <label htmlFor="last-30-days" key={i}>
              <input
                id="last-30-days"
                type="radio"
                value={LAST_30_DAYS}
                name={statements[index].id}
                checked={statements[index].activeRangeType === LAST_30_DAYS}
                onChange={(e) => handleRangeChange(index, e.target.value)}
              />
              <span className="statement-cycle title">{filterObj.displayName}</span>
            </label>
          );
        case LAST_THREE_MONTH:
          return (
            <label htmlFor="last-three-months" key={i}>
              <input
                id="last-three-months"
                type="radio"
                value={LAST_THREE_MONTH}
                name={statements[index].id}
                checked={statements[index].activeRangeType === LAST_THREE_MONTH}
                onChange={(e) => handleRangeChange(index, e.target.value)}
              />
              <span className="statement-cycle title">{filterObj.displayName}</span>
            </label>
          );
        case LAST_SIX_MONTH:
          return (
            <label htmlFor="last-six-months" key={i}>
              <input
                id="last-six-months"
                type="radio"
                value={LAST_SIX_MONTH}
                name={statements[index].id}
                checked={statements[index].activeRangeType === LAST_SIX_MONTH}
                onChange={(e) => handleRangeChange(index, e.target.value)}
              />
              <span className="statement-cycle title">{filterObj.displayName}</span>
            </label>
          );
        case TILL_DATE:
          return (
            <label htmlFor="till-date-statement" key={i}>
              <input
                id="till-date-statement"
                type="radio"
                value={TILL_DATE}
                name={statements[index].id}
                checked={statements[index].activeRangeType === TILL_DATE}
                onChange={(e) => handleRangeChange(index, e.target.value)}
              />
              <span className="statement-cycle title">{filterObj.displayName}</span>
            </label>
          );
        case CUSTOM_TILL_DATE:
          return (
            <label htmlFor="custom-till-date" key={i} className="custom-date-filter-wrapper">
              <input
                id="custom-till-date"
                type="radio"
                value={CUSTOM_TILL_DATE}
                name={statements[index].id}
                checked={statements[index].activeRangeType === CUSTOM_TILL_DATE}
                onChange={(e) => handleRangeChange(index, e.target.value)}
              />
              {renderComponets(COMPONENT.CUSTOM_TILL_DATE_SELECTION, filterObj)}
            </label>
          );
        case MONTH_YEAR:
          return (
            <label htmlFor="month-statement" key={i}>
              <input
                id="month-statement"
                type="radio"
                value={MONTH_YEAR}
                name={statements[index].id}
                checked={statements[index].activeRangeType === MONTH_YEAR}
                onChange={(e) => handleRangeChange(index, e.target.value)}
              />
              {renderComponets(COMPONENT.MONTH_YEAR, filterObj)}
            </label>
          );
        case SELECT_DATE:
          return (
            <label htmlFor="select-date-statement">
              <input
                id="select-date-statement"
                type="radio"
                value={SELECT_DATE}
                name={statements[index].id}
                checked={statements[index].activeRangeType === SELECT_DATE}
                onChange={(e) => handleRangeChange(index, e.target.value)}
              />
              {renderComponets(COMPONENT.SELECT_DATE, filterObj)}
            </label>
          );
        case DATE_RANGE:
          return (
            <label htmlFor={statements[index].id} key={i}>
              <input
                id={statements[index].id}
                type="radio"
                value={DATE_RANGE}
                name={statements[index].id}
                checked={statements[index].activeRangeType === DATE_RANGE}
                onChange={(e) => handleRangeChange(index, e.target.value)}
              />
              {renderComponets(COMPONENT.DATE_RANGE, filterObj)}
            </label>
          );
        default:
          break;
      }
    });
  }


  return (
    <div className="statement-desc-holder">
      <div className="statement-type">{statementName}</div>
      <div className="statement-cycle-types">{renderStatementRange()}</div>
      {!isEmpty(buttonsList) ? (
        <div className="flex-container">
          {buttonsList.map((buttonVal) => renderComponets(COMPONENT.BUTTON, buttonVal))}
        </div>
      ) : (
        <div className="send-btn-holder">{renderComponets(COMPONENT.BUTTON, buttonValues)}</div>
      )}
    </div>
  );
}

Statements.propTypes = {
  index: PropTypes.number.isRequired,
  statementName: PropTypes.string.isRequired,
  statements: PropTypes.array.isRequired,
  handleRangeChange: PropTypes.func.isRequired,
  DATE_RANGE_OPTIONS: PropTypes.object.isRequired,
  updateMethods: PropTypes.object.isRequired,
  buttonValues: PropTypes.object,
  buttonsList: PropTypes.array,
};

Statements.defaultProps = {
  buttonValues: PropTypes.object,
  buttonsList: null,
};
