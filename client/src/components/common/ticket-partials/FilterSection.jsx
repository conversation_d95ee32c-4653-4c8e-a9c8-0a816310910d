import React from 'react';
import PropTypes from 'prop-types';

const FilterSection = ({
  sectionTitle,
  children,
}) => (
  <div className="push-20">
    <h4 className="font-w400 text-black push-10">
      {sectionTitle}
    </h4>
    <div className="form-group">
      <div className="col-xs-12">
        {children}
      </div>
    </div>
  </div>
);

FilterSection.propTypes = {
  sectionTitle: PropTypes.string.isRequired,
  children: PropTypes.any.isRequired,
};

export default FilterSection;
