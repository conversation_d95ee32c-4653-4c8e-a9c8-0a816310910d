import React from 'react';
import Select from 'react-select';
import PropTypes from 'prop-types';
import map from 'lodash/map';
import moment from 'moment';

function formatDaysOption(daysList, dateFormat) {
  return map(daysList, day => ({
    label: `> ${day} Days`,
    value: moment().subtract(day, 'days').format(dateFormat),
  }));
}

const DaysFilterSelect = (props) => {
  const {
    daysList,
    onSelect,
    dateFormat,
  } = props;
  return (
    <Select
      isClearable
      options={formatDaysOption(daysList, dateFormat)}
      onChange={onSelect}
    />
  );
};

DaysFilterSelect.propTypes = {
  daysList: PropTypes.array.isRequired,
  onSelect: PropTypes.func.isRequired,
  dateFormat: PropTypes.string,
};

DaysFilterSelect.defaultProps = {
  dateFormat: 'YYYY-MM-DD',
};

export default DaysFilterSelect;
