import React from 'react';
import PropTypes from 'prop-types';

import Button from '../ActionButton';

function onFormSubmit(event, searchText, onSearch) {
  event.preventDefault();
  if (!searchText.trim().length) {
    return;
  }
  onSearch(searchText.trim());
}

function onTextChange(event, clearBtnVisible, onChange, onClear) {
  const newText = event.target.value;
  onChange(newText);
  if (clearBtnVisible && !newText.length) {
    onClear();
  }
}

const TicketSearchBar = ({
  searchText,
  clearBtnVisible,
  onChange,
  onSearch,
  onClearSearch,
  errorInSearch,
}) => (
  <form onSubmit={(e) => onFormSubmit(e, searchText, onSearch)}>
    <div className="input-group input-group-lg push-30">
      <input
        type="text"
        className="form-control"
        placeholder="Search using Customer ID"
        value={searchText}
        onChange={
          (e) => onTextChange(e, clearBtnVisible, onChange, onClearSearch)
        }
      />
      <div className="input-group-btn">
        {
          clearBtnVisible
            ? (
              <Button.IconButton
                iconClass="fa fa-times text-light"
                onClickAction={onClearSearch}
              />
            )
            : (
              <Button.IconButton
                iconClass="fa fa-search text-light"
                onClickAction={(e) => onFormSubmit(e, searchText, onSearch)}
              />
            )
        }
      </div>
    </div>
    { errorInSearch && (
      <div className="row">
        <div className="col-sm-6 pull-t">
          <div className="has-error push-30">
            <div className="help-block">
              {errorInSearch}
            </div>
          </div>
        </div>
      </div>
    )}
  </form>
);

TicketSearchBar.propTypes = {
  searchText: PropTypes.string.isRequired,
  clearBtnVisible: PropTypes.bool.isRequired,
  onChange: PropTypes.func.isRequired,
  onSearch: PropTypes.func.isRequired,
  onClearSearch: PropTypes.func.isRequired,
  errorInSearch: PropTypes.string,
};

TicketSearchBar.defaultProps = {
  errorInSearch: null,
};

export default TicketSearchBar;
