import React from 'react';
import PropTypes from 'prop-types';
import className from 'classnames';
import '../../../styles/pills.css';

function getPillClass(pillId, activePillId, pillDisabled) {
  return className({
    active: pillId === activePillId,
    disabled: pillDisabled,
  });
}

function handleClickOnPill(event, pillId, activePillId, onPillSelect) {
  event.preventDefault();
  if (pillId === activePillId) {
    return;
  }
  onPillSelect(pillId);
}

const Pills = ({
  items,
  activeItemId,
  onPillSelect,
  disableAll,
}) => {
  const containerClass =
    className('NavPills__Container nav nav-pills nav-stacked push', {
      'NavPills__Container--disabled': disableAll,
    });

  return (
    <ul className={containerClass}>
      {items.map((item, index) => (
        <li
          key={index}
          className={getPillClass(item.id, activeItemId, item.disabled)}
        >
          <a
            href="#"
            onClick={
              e => handleClickOnPill(e, item.id, activeItemId, onPillSelect)
            }
          >
            <span className="badge pull-right">{item.count}</span>
            <i className={`fa fa-fw ${item.iconClass} push-5-r`} />
            {item.label}
          </a>
        </li>
      ))}
    </ul>
  );
};

Pills.propTypes = {
  items: PropTypes.array.isRequired,
  activeItemId: PropTypes.string,
  onPillSelect: PropTypes.func.isRequired,
  disableAll: PropTypes.bool,
};

Pills.defaultProps = {
  activeItemId: null,
  disableAll: false,
};

export default Pills;
