/* eslint no-mixed-operators: 0 */
import React from 'react';
import PropTypes from 'prop-types';
import range from 'lodash/range';
import isEmpty from 'lodash/isEmpty';
import first from 'lodash/first';
import last from 'lodash/last';

function gotoPage(event, pageNum, onChange) {
  event.preventDefault();
  onChange(pageNum);
}

function calculateDisplayPages(totalPages, paginationSize, currentPage) {
  const numPaginationLinks = paginationSize > totalPages ? totalPages : paginationSize;
  const fixingPos = Math.floor(numPaginationLinks / 2) + 1;
  let startPageNumber = currentPage - fixingPos + 1;
  if (startPageNumber < 1) {
    startPageNumber = 1;
  }
  if (totalPages < startPageNumber + numPaginationLinks - 1) {
    startPageNumber = totalPages - numPaginationLinks + 1;
  }
  const displayedPages = range(startPageNumber, startPageNumber + numPaginationLinks);
  return displayedPages;
}

const Pagination = ({
  paginationSize,
  totalPages,
  currentPage,
  onPageChange,
  firstPage,
  lastPage,
  prevPage,
  nextPage,
  displayNumbers,
}) => {
  const displayedPages = calculateDisplayPages(totalPages, paginationSize, currentPage);
  return (
    <ul className="pagination">
      {
        !displayNumbers && <span>{`Showing page - ${currentPage}`}</span>
      }
      {
        // "First page" link
        !isEmpty(displayedPages) && first(displayedPages) !== 1 ? (
          <li className="page-item" title="First page">
            <a
              href="#"
              className="page-link"
              onClick={e => gotoPage(e, 1, onPageChange)}
            >
              {firstPage}
            </a>
          </li>
        )
          : null
      }
      {
        // "Previous page" link
        currentPage > 1 ? (
          <li className="page-item" title="Previous page">
            <a
              href="#"
              className="page-link"
              onClick={e => gotoPage(e, currentPage - 1, onPageChange)}
            >
              {prevPage}
            </a>
          </li>
        )
          : null
      }

      {
        // Page number links
        displayNumbers && displayedPages.map(pageNum => (
          <li
            key={pageNum}
            title={pageNum}
            className={`page-item ${pageNum === currentPage ? 'active' : ''}`}
          >
            <a
              href="#"
              className="page-link"
              onClick={e => gotoPage(e, pageNum, onPageChange)}
            >
              {pageNum}
            </a>
          </li>
        ))
      }

      {
        // "Next page" link
        currentPage < totalPages ? (
          <li className="page-item" title="Next page">
            <a
              href="#"
              className="page-link"
              onClick={e => gotoPage(e, currentPage + 1, onPageChange)}
            >
              {nextPage}
            </a>
          </li>
        )
          : null
      }
      {
        // "Last page" link
        !isEmpty(displayedPages) && last(displayedPages) !== totalPages ? (
          <li className="page-item" title="Last page">
            <a
              href="#"
              className="page-link"
              onClick={e => gotoPage(e, totalPages, onPageChange)}
            >
              {lastPage}
            </a>
          </li>
        )
          : null
      }
    </ul>
  );
};

Pagination.propTypes = {
  paginationSize: PropTypes.number.isRequired,
  totalPages: PropTypes.number.isRequired,
  currentPage: PropTypes.number.isRequired,
  onPageChange: PropTypes.func.isRequired,
  firstPage: PropTypes.string,
  lastPage: PropTypes.string,
  prevPage: PropTypes.string,
  nextPage: PropTypes.string,
  displayNumbers: PropTypes.bool,
};

Pagination.defaultProps = {
  firstPage: 'First',
  lastPage: 'Last',
  prevPage: '<',
  nextPage: '>',
  displayNumbers: true,
};

export default Pagination;
