import React, { Component } from 'react';
import PropTypes from 'prop-types';
import autobind from 'autobind-decorator';
import findIndex from 'lodash/findIndex';
import { PtmCheckbox } from '@paytm-money/frontend-common-v2';


// Creating the array of the checkbox selected state, corresponding to their indexes
function getCheckedStateFromGroupValue(checkboxGroupValue, checkboxItems) {
  const checkedData = [];
  checkboxItems.forEach((item) => {
    const isChecked = checkboxGroupValue.indexOf(item.value) > -1;
    checkedData.push(isChecked);
  });
  return checkedData;
}

class CheckboxGroup extends Component {
  constructor(props) {
    super(props);
    const checkedData = getCheckedStateFromGroupValue(props.value, props.items);
    const allChecked = !!(
      props.checkAllControlEnabled && this.checkIfAllSelected(props.value)
    );
    this.state = {
      checkedData,
      allChecked,
    };
  }

  componentWillReceiveProps(nextProps) {
    if (
      this.props.value !== nextProps.value ||
      this.props.items !== nextProps.items
    ) {
      const checkedData =
        getCheckedStateFromGroupValue(nextProps.value, nextProps.items);
      const allChecked = !!(
        nextProps.checkAllControlEnabled &&
        this.checkIfAllSelected(nextProps.value)
      );
      this.setState({
        checkedData,
        allChecked,
      });
    }
  }

  @autobind
  onCheckboxChange(event) {
    const { target } = event;
    const { value, checked } = target;
    const { items, onChange } = this.props;
    const { checkedData } = this.state;
    const currentCheckboxIndex = findIndex(items, obj => String(obj.value) === value);
    checkedData[currentCheckboxIndex] = checked;
    const updatedCheckboxGroupVal =
      items.filter((item, index) => checkedData[index])
        .map(item => item.value);
    onChange(updatedCheckboxGroupVal);
  }

  @autobind
  onAllCheckboxChange(event) {
    const { checked } = event.target;
    const { items, onChange } = this.props;
    const updatedCheckboxGroupVal = checked ?
      items.map(item => item.value) : [];
    onChange(updatedCheckboxGroupVal);
  }

  checkIfAllSelected(checkboxGroupValue) {
    return (
      this.props.items.length &&
      checkboxGroupValue.length === this.props.items.length
    );
  }

  render() {
    const {
      items,
      name,
      disableAll,
      checkAllControlEnabled,
      scrollable,
      maxHeight,
    } = this.props;
    const {
      checkedData,
      allChecked,
    } = this.state;

    return (
      <div
        className="row"
        style={scrollable ?
          {
            maxHeight,
            overflowY: 'auto',
          }
        : {}}
      >
        {
          checkAllControlEnabled && items.length ?
            <div className="col-xs-12">
              <PtmCheckbox
                id="all"
                label="All"
                checked={allChecked}
                onChange={this.onAllCheckboxChange}
              />
            </div>
          : null
        }
        {items.map((item, index) => (
          <div key={index} className="col-xs-12">
            <PtmCheckbox
              id={`${name}-${index}`}
              name={`${name}-${index}`}
              label={item.label}
              checked={checkedData[index]}
              value={item.value}
              disabled={disableAll || item.disabled}
              onChange={this.onCheckboxChange}
            />
          </div>
        ))}
      </div>
    );
  }
}

const ValueProps = PropTypes.oneOfType([
  PropTypes.string,
  PropTypes.number,
]);

CheckboxGroup.propTypes = {
  items: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string,
    value: ValueProps,
  })).isRequired,
  // array of selected value, should be one of the value in the items
  value: PropTypes.oneOfType([
    PropTypes.arrayOf(ValueProps),
    PropTypes.string,
  ]).isRequired,
  name: PropTypes.string,
  disableAll: PropTypes.bool,
  checkAllControlEnabled: PropTypes.bool,
  scrollable: PropTypes.bool,
  maxHeight: PropTypes.number,
  onChange: PropTypes.func.isRequired,
};

CheckboxGroup.defaultProps = {
  name: '',
  disableAll: false,
  checkAllControlEnabled: false,
  scrollable: false,
  maxHeight: 180,
};

export default CheckboxGroup;
