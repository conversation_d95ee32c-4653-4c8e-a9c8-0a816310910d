import React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';

const Block = ({
  shouldRemoveBg,
  children,
}) => {
  const blockClass = classNames(
    'block',
    { 'transparent-bg': shouldRemoveBg },
  );
  return (
    <div className={blockClass}>
      {children}
    </div>
  );
};

Block.propTypes = {
  children: PropTypes.any.isRequired,
};

export const BlockHeader = ({
  headerClasses,
  title,
}) => (
  <div className={`block-header ${headerClasses}`}>
    <h3 className="block-title">
      {title}
    </h3>
  </div>
);

BlockHeader.propTypes = {
  headerClasses: PropTypes.string,
  title: PropTypes.string.isRequired,
};

BlockHeader.defaultProps = {
  headerClasses: '',
};

export const BlockContent = ({
  contentClasses,
  children,
}) => (
  <div className={`block-content ${contentClasses}`}>
    {children}
  </div>
);

Block.propTypes = {
  shouldRemoveBg: PropTypes.bool,
};

Block.defaultProps = {
  shouldRemoveBg: false,
};

BlockContent.propTypes = {
  contentClasses: PropTypes.string,
  children: PropTypes.any.isRequired,
};

BlockContent.defaultProps = {
  contentClasses: '',
};

export default Block;
