import React from 'react';
import PropTypes from 'prop-types';
import Modal from 'react-modal';
import '../../styles/customer-details-modal.css';


const GenericModal = ({
  onClose,
  children,
  isOpen,
  overlayClassName,
  ...rest
}) => (
  <Modal
    ariaHideApp={false}
    overlayClassName={overlayClassName}
    isOpen={isOpen}
    shouldCloseOnEsc
    onRequestClose={onClose}
    {...rest}
  >
    <div>
      <div
        role="button"
        className="cd-md-close-btn"
        onClick={onClose}
      >
        <i className="fa fa-2x fa-window-close" />
      </div>
      { children }
    </div>
  </Modal>
);

GenericModal.propTypes = {
  overlayClassName: PropTypes.string,
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  children: PropTypes.any,
};

GenericModal.defaultProps = {
  overlayClassName: 'cd-md-container',
  children: null,
};

export default GenericModal;
