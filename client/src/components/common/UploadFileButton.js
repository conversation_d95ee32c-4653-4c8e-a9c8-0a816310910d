import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';

import '../../styles/helper.scss';

const UploadFileButton = (props) => {
  const {
    loading, fileUpload, disabled, label, isUploaded, acceptType,
  } = props;

  if (isUploaded) {
    return (
      <div className="upload-finished text-center text-12 width-150px padding-10-15">
        <i className="fa fa-check-circle color-green margin-right-10" aria-hidden="true" />
        File Uploaded
      </div>
    );
  }

  return (
    <div className="upload-file-btn">
      {
        loading
          ? <label>Uploading...</label>
          : (
            <label>
              {label}
              <input
                disabled={disabled}
                type="file"
                onChange={(e) => {
                  fileUpload(e.target.files[0]);
                  e.target.value = null;
                }}
                accept={acceptType}
              />
            </label>
          )
      }
    </div>
  );
};

UploadFileButton.propTypes = {
  loading: PropTypes.bool,
  fileUpload: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  label: PropTypes.bool,
  isUploaded: PropTypes.bool,
  acceptType: PropTypes.string,
};

UploadFileButton.defaultProps = {
  loading: false,
  disabled: false,
  label: 'Upload',
  isUploaded: false,
  acceptType: '*',
};

export default UploadFileButton;
