import React from 'react';
import PropTypes from 'prop-types';
import '../../styles/error-page.css';

const ErrorSection = ({ errorMsg, containerClass, basicErrMsg, basicErrImg }) => {
  const containerClasses = `${containerClass} error-page-container`;
  return (
    <div className={containerClasses}>
      {basicErrImg
      && (<img src="/assets/img/error.jpg" alt="error" />)}
      {basicErrMsg
      && (<div className="error-msg">Something went wrong! Don&apos;t worry, we&apos;re looking into it</div>)}
      <div className="push-10-t h4"><b>Error: </b>{errorMsg}</div>
    </div>
  );
};

ErrorSection.propTypes = {
  errorMsg: PropTypes.string,
  containerClass: PropTypes.string,
  basicErrMsg: PropTypes.bool,
  basicErrImg: PropTypes.bool,
};

ErrorSection.defaultProps = {
  errorMsg: '',
  containerClass: 'col-sm-12 col-lg-9',
  basicErrMsg: true,
  basicErrImg: true,
};

export default ErrorSection;
