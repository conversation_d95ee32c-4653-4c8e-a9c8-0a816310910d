import React, { useRef } from 'react';
import PropTypes from 'prop-types';
import { PtmButton } from '@paytm-money/frontend-common-v2';

import ImageCropper from '../ImageCropper';
import ImageBlurEditor from '../ImageBlurEditor';
import { IMG_WIDTH_HEIGHT_BOUNDRY, MAX_BOUNDRY } from './util';

const ROTATE_DIRECTION_ANGLE_MAP = {
  RIGHT: 90,
  LEFT: -90,
};

const ImageEditorView = (props) => {
  const {
    showCropEditor,
    showBlurEditor,
    imageSrc,
    onFinishEdit,
    updateImage,
    rotateImage,
    saveImage,
    saveAsPNG,
    loadCropper,
    showBlurOption,
    loadBlurEditor,
    closeEditModal,
    minCropAttributes,
    showRotateOption,
    showCropOption,
  } = props;
  const image = useRef(null);
  let initialSelectedArea;
  const minCropArea = {
    minWidth: 0,
    minHeight: 0,
  };
  if (showCropEditor) {
    initialSelectedArea = {
      x: 10, y: 10, width: 80, height: 80,
    };
    const imageDimentionsCheck = minCropAttributes
    && minCropAttributes.height
    && minCropAttributes.width
    && image.current
    && image.current.width
    && image.current.height;

    if (imageDimentionsCheck) {
      minCropArea.minWidth = (minCropAttributes.width / image.current.width) * MAX_BOUNDRY;
      minCropArea.minHeight = (minCropAttributes.height / image.current.height) * MAX_BOUNDRY;
      if (minCropArea.minWidth > IMG_WIDTH_HEIGHT_BOUNDRY && minCropArea.minWidth <= MAX_BOUNDRY) {
        initialSelectedArea.width = minCropArea.minWidth;
      } else if (minCropArea.minWidth > MAX_BOUNDRY) {
        initialSelectedArea.width = MAX_BOUNDRY;
      } else {
        initialSelectedArea.width = IMG_WIDTH_HEIGHT_BOUNDRY;
      }
      if (minCropArea.minHeight > IMG_WIDTH_HEIGHT_BOUNDRY && minCropArea.minHeight <= MAX_BOUNDRY) {
        initialSelectedArea.height = minCropArea.minHeight;
      } else if (minCropArea.minHeight > MAX_BOUNDRY) {
        initialSelectedArea.height = MAX_BOUNDRY;
      } else {
        initialSelectedArea.height = IMG_WIDTH_HEIGHT_BOUNDRY;
      }
    }
    return (
      <ImageCropper
        imageSrc={imageSrc}
        onFinishEdit={onFinishEdit}
        updateImage={updateImage}
        saveAsPNG={saveAsPNG}
        minCropArea={minCropArea}
        initialSelectedArea={initialSelectedArea}
      />
    );
  }

  if (showBlurEditor) {
    initialSelectedArea = {
      x: 30, y: 67, width: 30, height: 12,
    };
    return (
      <ImageBlurEditor
        imageSrc={imageSrc}
        onFinishEdit={onFinishEdit}
        updateImage={updateImage}
        saveAsPNG={saveAsPNG}
        initialSelectedArea={initialSelectedArea}
      />
    );
  }

  return (
    <div className="container">
      <div className="imageContainer">
        <img
          ref={image}
          src={imageSrc}
          alt="mainImage"
          crossOrigin="use-credentials"
          className="editable-img"
        />
      </div>
      <div className="control-btn">
        <div>
          {showCropOption && (
          <PtmButton
            name="load-cropper"
            appearance="primaryDark"
            handlePress={loadCropper}
            className="push-10-t push-10-r"
          >
            Crop
          </PtmButton>
          )}
          {showRotateOption && (
            <>
              <PtmButton
                name="rotate-left"
                appearance="primaryDark"
                handlePress={() => rotateImage(ROTATE_DIRECTION_ANGLE_MAP.LEFT)}
                className="push-10-t push-10-r"
              >
                Rotate Left
              </PtmButton>
              <PtmButton
                name="rotate-right"
                appearance="primaryDark"
                handlePress={() => rotateImage(ROTATE_DIRECTION_ANGLE_MAP.RIGHT)}
                className="push-10-t push-10-r"
              >
                Rotate Right
              </PtmButton>
            </>
          )}
          {
            showBlurOption && (
              <PtmButton
                name="load-blur"
                appearance="primaryDark"
                handlePress={loadBlurEditor}
                className="push-10-t"
              >
                Blur Content
              </PtmButton>
            )
          }
        </div>
        <div>
          <PtmButton
            appearance="primaryDark"
            handlePress={saveImage}
            className="push-30-t push-10-r"
          >
            Save
          </PtmButton>
          <PtmButton
            appearance="primaryDark"
            handlePress={closeEditModal}
            className="push-30-t"
          >
            Cancel
          </PtmButton>
        </div>
      </div>
    </div>
  );
};

export default ImageEditorView;

ImageEditorView.propTypes = {
  showCropEditor: PropTypes.bool.isRequired,
  showBlurEditor: PropTypes.bool.isRequired,
  saveAsPNG: PropTypes.bool.isRequired,
  showBlurOption: PropTypes.bool.isRequired,
  imageSrc: PropTypes.string.isRequired,
  onFinishEdit: PropTypes.func.isRequired,
  updateImage: PropTypes.func.isRequired,
  rotateImage: PropTypes.func.isRequired,
  saveImage: PropTypes.func.isRequired,
  loadCropper: PropTypes.func.isRequired,
  loadBlurEditor: PropTypes.func.isRequired,
  closeEditModal: PropTypes.func.isRequired,
  minCropAttributes: PropTypes.object,
  showRotateOption: PropTypes.bool.isRequired,
  showCropOption: PropTypes.bool.isRequired,
};

ImageEditorView.defaultProps = {
  minCropAttributes: {},
};
