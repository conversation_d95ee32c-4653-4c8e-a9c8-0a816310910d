import React from 'react';
import PropTypes from 'prop-types';

import ImageEditorView from './ImageEditorView';
import Loader from '../Loader';

class ImageEditorContainer extends React.Component {
  constructor(props) {
    super(props);
    const { imageSrc } = props;
    this.state = {
      showCropEditor: false,
      showBlurEditor: false,
      imageSrc,
      imageConvertedToBlob: false,
    };
    this.blob = null;
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.imageSrc !== this.state.imageSrc) {
      const { imageSrc } = nextProps;
      this.setState({
        imageSrc,
      });
    }
  }

  onLoadCropper = () => {
    this.setState({
      showCropEditor: true,
    });
  }

  onLoadBlurEditor = () => {
    this.setState({
      showBlurEditor: true,
    });
  }

  onFinishEdit = () => {
    this.setState({
      showCropEditor: false,
      showBlurEditor: false,
    });
  }

  onUpdateImage = (blob, imageSrc) => {
    this.blob = blob;
    this.setState({
      imageSrc,
    });
  }

  onSaveImage = () => {
    this.props.onSave(this.blob, this.state.imageSrc);
    this.onCloseEditModal();
  }

  onCloseEditModal = () => {
    const { imageSrc, closeModal } = this.props;
    this.setState({
      imageSrc,
      showCropEditor: false,
    });
    closeModal();
  }

  onRotateImage = (direction) => {
    const canvas = document.getElementById('canvas');
    const ctx = canvas.getContext('2d');

    const degrees = direction;

    let width = 0;
    let height = 0;
    let diagonal = 0;
    const { image } = this;

    width = image.naturalWidth;
    height = image.naturalHeight;
    diagonal = Math.sqrt((width ** 2) + (height ** 2));

    ctx.canvas.height = diagonal;
    ctx.canvas.width = diagonal;

    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.save();
    ctx.translate(diagonal / 2, diagonal / 2);
    ctx.rotate((degrees * Math.PI) / 180);
    ctx.drawImage(image, -width / 2, -height / 2);
    ctx.restore();

    const trimmedCanvas = this.trimCanvas(canvas);
    const { saveAsPNG } = this.props;
    const fileType = saveAsPNG ? 'image/png' : 'image/jpeg';

    trimmedCanvas.toBlob((blobObject) => {
      this.blob = blobObject;
      this.setState({
        imageSrc: window.URL.createObjectURL(blobObject),
      });
    }, fileType);
  }

  trimCanvas = (c) => {
    const ctx = c.getContext('2d');
    const copy = document.createElement('canvas').getContext('2d');
    const pixels = ctx.getImageData(0, 0, c.width, c.height);
    const l = pixels.data.length;
    let i;
    const bound = {
      top: null,
      left: null,
      right: null,
      bottom: null,
    };
    let x;
    let y;

    // Iterate over every pixel to find the highest
    // and where it ends on every axis ()
    for (i = 0; i < l; i += 4) {
      if (pixels.data[i + 3] !== 0) {
        x = (i / 4) % c.width;
        y = ~~((i / 4) / c.width);

        if (bound.top === null) {
          bound.top = y;
        }

        if (bound.left === null) {
          bound.left = x;
        } else if (x < bound.left) {
          bound.left = x;
        }

        if (bound.right === null) {
          bound.right = x;
        } else if (bound.right < x) {
          bound.right = x;
        }

        if (bound.bottom === null) {
          bound.bottom = y;
        } else if (bound.bottom < y) {
          bound.bottom = y;
        }
      }
    }

    // Calculate the height and width of the content
    const trimHeight = bound.bottom - bound.top;
    const trimWidth = bound.right - bound.left;
    const trimmed = ctx.getImageData(bound.left, bound.top, trimWidth, trimHeight);

    copy.canvas.width = trimWidth;
    copy.canvas.height = trimHeight;
    copy.putImageData(trimmed, 0, 0);

    // Return trimmed canvas
    return copy.canvas;
  }

  convertImgSrcToBlob = () => {
    if (!this.state.imageConvertedToBlob) {
      const img = this.image;
      const canvas = document.createElement('canvas');
      canvas.width = img.naturalWidth;
      canvas.height = img.naturalHeight;
      const ctx = canvas.getContext('2d');
      ctx.drawImage(img, 0, 0);

      const { saveAsPNG } = this.props;
      const fileType = saveAsPNG ? 'image/png' : 'image/jpeg';

      canvas.toBlob((blobObject) => {
        if (blobObject) {
          this.blob = blobObject;
          this.setState({
            imageConvertedToBlob: true,
            imageSrc: window.URL.createObjectURL(blobObject),
          });
        }
      }, fileType);
    }
  }

  render() {
    const {
      showCropEditor,
      showBlurEditor,
      imageSrc,
      imageConvertedToBlob,
    } = this.state;
    const {
      showBlurOption, saveAsPNG, minCropAttributes, showRotateOption, showCropOption
    } = this.props;
    return (
      <div>
        {!imageConvertedToBlob ? (
          <div>
            <Loader.ContentLoader />
          </div>
        ) : (
          <ImageEditorView
            showCropEditor={showCropEditor}
            showBlurEditor={showBlurEditor}
            showBlurOption={showBlurOption}
            showRotateOption={showRotateOption}
            showCropOption={showCropOption}
            imageSrc={imageSrc}
            minCropAttributes={minCropAttributes}
            saveAsPNG={saveAsPNG}
            onFinishEdit={this.onFinishEdit}
            updateImage={this.onUpdateImage}
            rotateImage={this.onRotateImage}
            saveImage={this.onSaveImage}
            loadCropper={this.onLoadCropper}
            loadBlurEditor={this.onLoadBlurEditor}
            closeEditModal={this.onCloseEditModal}
          />
        )}
        <div className="hide">
          <canvas id="canvas" width="300" height="300" />
          <img
            ref={(el) => {
              this.image = el;
            }}
            alt="no show"
            crossOrigin="use-credentials"
            src={imageSrc}
            width="250"
            onLoad={this.convertImgSrcToBlob}
          />
        </div>
      </div>
    );
  }
}

export default ImageEditorContainer;

ImageEditorContainer.propTypes = {
  closeModal: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  imageSrc: PropTypes.string.isRequired,
  saveAsPNG: PropTypes.bool,
  showBlurOption: PropTypes.bool.isRequired,
  showRotateOption: PropTypes.bool.isRequired,
  showCropOption: PropTypes.bool.isRequired,
  minCropAttributes: PropTypes.object.isRequired,
};

ImageEditorContainer.defaultProps = {
  saveAsPNG: false,
};
