.edit-pop-up {
    z-index: 2000 !important;
}
.buttonParent {
    text-align: right;
    padding: 0;
}
.sm-btn {
    min-width: 96px;
    background: #3f4b5a;
    border-color: #3f4b5a;
    color: white;
    border-radius: 4px;
}

.disabled-btn{
    opacity: .5;
}

.no-padding {
    padding: 0;
}
.left-align {
    text-align: left
}
.container {
    width: 500px;
    height: 610px; 
    text-align: center;
}
.imageContainer {
    height: 500px;
    display: table-cell;
    vertical-align: middle;
    text-align: center;
    width: 500px;
}
.editable-img {
    max-width: 100%;
    max-height: 500px;
}
.control-btn {
    position: absolute;
    bottom: 20px;
    width: 93%;
}
.hide {
    display: none;
}
.width-20 {
    width: 20%;
}
.push-right {
    position: relative;
    right: 10px;
}
