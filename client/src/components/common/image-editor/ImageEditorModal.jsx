import React, { Component } from 'react';
import PropTypes from 'prop-types';
import Modal from 'react-modal';
import { PtmButton } from '@paytm-money/frontend-common-v2';

import ImageEditor from './ImageEditorContainer';
import './styles.css';

const modalStyle = {
  content: {
    top: '50%',
    left: '50%',
    right: 'auto',
    bottom: 'auto',
    marginRight: '-50%',
    transform: 'translate(-50%, -50%)',
    zIndex: 1090,
    width: '570px',
    height: '650px',
  },
};

class ImageEditorModal extends Component {
  constructor(props) {
    super(props);
    this.state = {
      src: props.imgUrl,
      showEditModal: false,
    };
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.imgUrl !== this.props.imgUrl) {
      this.setState({
        src: nextProps.imgUrl,
      });
    }
  }

  openEditModal = () => {
    this.setState({
      showEditModal: true,
    });
  }

  closeModal = () => {
    this.setState({ showEditModal: false });
  }

  render() {
    const {
      src,
      showEditModal,
    } = this.state;

    const {
      saveAsPNG,
      showBlurOption,
      showRotateOption,
      showCropOption,
      parentContainer,
      onSave,
      minCropAttributes,
    } = this.props;

    return (
      <div className={parentContainer}>
        <div className="pad-10-t text-black">
          <PtmButton
            appearance="primaryDark"
            handlePress={this.openEditModal}
          >
            Edit Image
          </PtmButton>
        </div>
        <div>
          <Modal
            isOpen={showEditModal}
            onRequestClose={this.closeModal}
            style={modalStyle}
            contentLabel="Edit Modal"
          >
            <ImageEditor
              closeModal={this.closeModal}
              minCropAttributes={minCropAttributes}
              imageSrc={src}
              onSave={onSave}
              saveAsPNG={saveAsPNG}
              showBlurOption={showBlurOption}
              showRotateOption={showRotateOption}
              showCropOption={showCropOption}
            />
          </Modal>
        </div>
      </div>
    );
  }
}

ImageEditorModal.propTypes = {
  imgUrl: PropTypes.string.isRequired,
  onSave: PropTypes.func.isRequired,
  saveAsPNG: PropTypes.bool,
  showBlurOption: PropTypes.bool,
  parentContainer: PropTypes.string,
  minCropAttributes: PropTypes.object,
  showRotateOption: PropTypes.bool,
  showCropOption: PropTypes.bool,
};

ImageEditorModal.defaultProps = {
  saveAsPNG: false,
  showBlurOption: false,
  parentContainer: '',
  minCropAttributes: {},
  showRotateOption: true,
  showCropOption: true,
};

export default ImageEditorModal;
