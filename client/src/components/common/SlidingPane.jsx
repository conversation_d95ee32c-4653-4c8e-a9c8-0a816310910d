import React from 'react';
import PropTypes from 'prop-types';
import Modal from 'react-modal';

import '../../styles/sliding-pane.css';

const CLOSE_TIMEOUT = 500; // in ms

// Reference: https://github.com/DimitryDushkin/sliding-pane
const SlidingPane = ({
  isOpen,
  title,
  subtitle,
  onRequestClose,
  onAfterOpen,
  children,
  className,
  overlayClassName,
  from,
  width,
}) => {
  const directionClass = `slide-pane_from_${from}`;
  return (
    <Modal
      overlayClassName={`slide-pane__overlay ${overlayClassName || ''}`}
      ariaHideApp={false}
      className={`slide-pane ${directionClass} ${className || ''}`}
      style={{ content: { width } }}
      closeTimeoutMS={CLOSE_TIMEOUT}
      isOpen={isOpen}
      onAfterOpen={onAfterOpen}
      onRequestClose={onRequestClose}
      contentLabel={`Modal "${title || ''}"`}
    >
      <div className="slide-pane__header">
        <div className="slide-pane__title-wrapper">
          { title }
          <div className="slide-pane__subtitle">{ subtitle }</div>
        </div>
        <button className="btn btn-default pull-right push-20-r" type="button" onClick={onRequestClose}>
          <i className="fa fa-times" />
        </button>
      </div>
      <div className="slide-pane__content">
        { children }
      </div>
    </Modal>
  );
};

SlidingPane.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  title: PropTypes.any,
  subtitle: PropTypes.any,
  onRequestClose: PropTypes.func.isRequired,
  onAfterOpen: PropTypes.func,
  children: PropTypes.any.isRequired,
  className: PropTypes.string,
  overlayClassName: PropTypes.string,
  from: PropTypes.oneOf(['left', 'right']),
  width: PropTypes.string,
};

SlidingPane.defaultProps = {
  from: 'right',
  width: '60%',
  className: '',
  overlayClassName: '',
  title: null,
  subtitle: null,
  onAfterOpen: () => {},
};

export default SlidingPane;
