import React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import { PtmInput } from '@paytm-money/frontend-common-v2';

import './edit-table-styles.scss';

const EditTable = ({
  tableKey, heading, rowsConfig, valuesMapping, handleChange,
}) => {
  const styles = {
    contentHeading: 'edit-table__content-heading',
    contentRowsWrapper: 'edit-table__content-rows-wrapper',
    contentRow: 'edit-table__content-row',
    contentRowInput: 'edit-table__content-row-input',
    contentRowEditButton: 'edit-table__content-row-edit-button',
  };

  const [activeIndex, setActiveIndex] = React.useState(null);

  return valuesMapping[tableKey] ? (
    <div>
      <h4 className={styles.contentHeading}>{heading}</h4>
      <table className={styles.contentRowsWrapper}>
        {rowsConfig.map((tableRow, index) => (
          <tr className={styles.contentRow}>
            <td>{tableRow.fieldName}</td>
            <td className={classNames({ [styles.contentRowInput]: activeIndex === index })}>
              {activeIndex === index ? (
                <PtmInput
                  id={`${heading}-${index}`}
                  value={valuesMapping[tableKey][tableRow.key] || ''}
                  onChange={(e) => handleChange(tableKey, tableRow.key, e.target.value)}
                  roundedCorners
                />
              ) : `${valuesMapping[tableKey][tableRow.key] || ''}`}
            </td>
            <td>
              <button className={styles.contentRowEditButton} type="button" onClick={() => setActiveIndex(index)}>
                <i className={'fa fa-edit'} />
              </button>
            </td>
          </tr>
        ))}
      </table>
    </div>
  ) : null;
};

const EditTableContainer = ({ config, handleInputChange, valuesMapping }) => {
  const styles = {
    container: 'edit-table__container',
    header: 'edit-table__header',
  };

  const getHeader = () => (
    <div className={styles.header}>
      <p>Field</p>
      <p>Value</p>
      <p>Action</p>
    </div>
  );

  return (
    <div className={styles.container}>
      {getHeader()}
      {config.map((tableConfig) => (
        <EditTable
          tableKey={tableConfig.key}
          heading={tableConfig.heading}
          rowsConfig={tableConfig.rowsConfig}
          valuesMapping={valuesMapping}
          handleChange={handleInputChange}
        />
      ))}
    </div>
  );
};

EditTable.propTypes = {
  tableKey: PropTypes.string.isRequired,
  heading: PropTypes.string.isRequired,
  rowsConfig: PropTypes.array.isRequired,
  handleChange: PropTypes.func.isRequired,
  valuesMapping: PropTypes.object.isRequired,
};

EditTableContainer.propTypes = {
  config: PropTypes.array.isRequired,
  handleInputChange: PropTypes.func.isRequired,
  valuesMapping: PropTypes.object.isRequired,
};

export default EditTableContainer;
