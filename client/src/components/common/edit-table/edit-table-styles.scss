.edit-table {
  &__container {
    width: 100%;
  }
  &__header {
    background: #3e4a59;
    color: white;
    display: flex;
    margin: 0;
    > * {
      flex: 1 0 40%;
      padding: 15px;
      border: rgba(62, 74, 89, 0.15) 1.3px solid;
      margin: 0;
    }
    > *:last-child {
      flex: 1 0 20%;
    }
  }
  &__content {
    &-heading {
      background: #dfe3e5;
      color: #2e384d;
      padding: 15px;
      width: 100%;
      size: 14px;
      line-height: 22px;
    }
    &-rows-wrapper {
      background: white;
      width: 100%;
      & > div:nth-child(2n) {
        background: #f3f5f7;
      }
    }
    &-row {
      display: flex;
      > * {
        flex: 1 0 40%;
        padding: 15px;
        border: rgba(62, 74, 89, 0.15) 1.3px solid;
      }
      :last-child {
        flex: 1 0 20%;
      }
      &-input {
        padding: 8px;
        :first-child {
          margin-bottom: 0;
        }
      }
      &-edit-button {
        background: transparent;
        outline: 0;
        border: 0;
        font-size: 16px;
      }
    }
  }
}