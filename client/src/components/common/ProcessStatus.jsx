import React from 'react';
import PropTypes from 'prop-types';

const ProcessStatus = ({
  iconClass,
  textClass,
  text,
  source,
  containerClass,
  rejectionMsg,
  verificationSource,
}) => (
  <div className={`col-xs-6 col-sm-3 ${containerClass}`} style={{ minWidth: 200 }}>
    <p className={`${textClass} remove-margin-b`}>
      <i className={`fa ${iconClass} fa-lg`} aria-hidden="true" />
      <span className="push-5-l">{text}</span>
    </p>
    {source && <span className="doc-source width-100 pad-10-t pad-10">{`Uploaded Via: ${source}`}</span>}
    {verificationSource && <span className="doc-source margin-bottom-15">{`Verified Via: ${verificationSource}`}</span>}
    {rejectionMsg && <span>{rejectionMsg}</span>}
  </div>
);

export default ProcessStatus;

ProcessStatus.propTypes = {
  iconClass: PropTypes.string,
  textClass: PropTypes.string,
  text: PropTypes.string,
  source: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  verificationSource: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.number,
  ]),
  containerClass: PropTypes.string,
  rejectionMsg: PropTypes.string,
};

ProcessStatus.defaultProps = {
  iconClass: '',
  textClass: '',
  text: '',
  source: null,
  verificationSource: null,
  containerClass: '',
  rejectionMsg: null,
};
