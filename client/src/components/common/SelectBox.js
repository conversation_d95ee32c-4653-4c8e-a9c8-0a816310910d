import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import Select from 'react-select';

// import '../../../../../styles/helper.scss';

const customStyle = {
  control: () => ({
    borderRadius: '5px',
    border: '1px solid #3e4a59',
    color: '#3e4a59',
    minHeight: '30px',
    height: '35px',
    background: 'transparent',
    display: 'flex',
  }),
};

const SelectBox = (props) => {
  const {
    options, onChange, value, isClearable = true, styles = { ...customStyle }, ...restProps
  } = props;

  return (
    <Select
      isClearable={isClearable}
      styles={styles}
      options={options}
      onChange={onChange}
      value={value}
      {...restProps}
    />
  );
};

SelectBox.propTypes = {
  options: PropTypes.array.isRequired,
  onChange: PropTypes.func.isRequired,
  value: PropTypes.object,
  isClearable: PropTypes.bool,
  autoFocus: PropTypes.bool,
  className: PropTypes.string,
  classNamePrefix: PropTypes.string,
  isDisabled: PropTypes.bool,
  isSearchable: PropTypes.bool,
  placeholder: PropTypes.string,
  noOptionsMessage: PropTypes.string,
  styles: PropTypes.object,
};

export default SelectBox;
