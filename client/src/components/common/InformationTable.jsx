import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { PtmButton } from '@paytm-money/frontend-common-v2';
import autobind from 'autobind-decorator';
import slice from 'lodash/slice';

export default class InformationTable extends Component {
  static renderRow(row, rowIndex) {
    return (
      <tr key={`row_${rowIndex}`}>
        {
          row.map((cell, index) => {
            if (React.isValidElement(cell)) {
              return cell;
            }
            return (
              <td
                key={`data_row_${index}`}
                className="text-center"
              >
                {cell}
              </td>
            );
          })
        }
      </tr>
    );
  }

  constructor() {
    super();
    this.state = { isOpen: false };
  }

  @autobind
  onBtnClick() {
    this.setState(prevState => ({ isOpen: !prevState.isOpen }));
    this.props.onViewMore();
  }

  renderTableData() {
    const {
      tableRowArr, rowInCollapsedState, showViewMore,
    } = this.props;
    let displayData = tableRowArr;
    if (showViewMore && !this.state.isOpen) {
      displayData = slice(tableRowArr, 0, rowInCollapsedState);
    }
    return displayData.map((row, index) => InformationTable.renderRow(row, index + 1));
  }

  renderHeaderRow() {
    const { headerRowArr } = this.props;
    return (
      <tr>
        {
          headerRowArr.map((heading, index) => {
            if (React.isValidElement(heading)) {
              return heading;
            }
            return (
              <th key={`head_${index}`} className="text-center">
                {heading}
              </th>
            );
          })
      }
      </tr>
    );
  }

  renderTable() {
    const { tableClass, showViewMore } = this.props;
    return (
      <div>
        <table className={tableClass}>
          <thead>
            {this.renderHeaderRow()}
          </thead>
          <tbody>
            {this.renderTableData()}
          </tbody>
        </table>
        {
          showViewMore
            ? (
              <div className="row text-center">
                <PtmButton
                  appearance="primaryDark"
                  handlePress={this.onBtnClick}
                >
                  {this.state.isOpen ? 'View Less' : 'View All'}
                </PtmButton>
              </div>
            )
            : null
        }
      </div>
    );
  }

  render() {
    const { isHeaderPresent, tableHeading } = this.props;
    if (!isHeaderPresent) {
      return this.renderTable();
    }
    return (
      <div className="block block-themed">
        <div className="block-header bg-primary-dark">
          <h3 className="block-title">{tableHeading}</h3>
        </div>
        <div className="block-content">
          {this.renderTable()}
        </div>
      </div>
    );
  }
}

InformationTable.propTypes = {
  isHeaderPresent: PropTypes.bool.isRequired,
  headerRowArr: PropTypes.array.isRequired, // eslint-disable-line react/forbid-prop-types
  tableRowArr: PropTypes.array.isRequired, // eslint-disable-line react/forbid-prop-types
  tableHeading: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.node,
  ]),
  tableClass: PropTypes.string,
  showViewMore: PropTypes.bool,
  rowInCollapsedState: PropTypes.number,
  onViewMore: PropTypes.func,
};

InformationTable.defaultProps = {
  tableHeading: '',
  tableClass: 'table table-bordered',
  showViewMore: true,
  rowInCollapsedState: 5,
  onViewMore: () => undefined,
};
