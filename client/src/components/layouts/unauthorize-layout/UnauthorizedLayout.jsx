import React, { Component } from 'react';
import { Switch, Route, Redirect } from 'react-router-dom';
import { connect } from 'react-redux';
import { bindActionCreators } from 'redux';
import { loginSuccess } from '../../../actions/auth-actions';
import '@paytm-money/frontend-common-v2/cdn/build/elements/elements';
import '@paytm-money/frontend-common-v2/cdn/build/login-module/login-module';
import '../../../styles/login-page.css';
import { COOKIES_CONFIG } from './config';

const loginPath = '/login/v2';

export class UnauthorizedLayout extends Component {
  constructor() {
    super();
    this.handeSearch = this.handeSearch.bind(this);
  }

  componentDidMount() {
    const { redirectPath, history } = this.props;
    if (redirectPath) {
      history.replace(`${loginPath}?redirect-path=${redirectPath}`);
    }
    document.addEventListener('login-success', this.handeSearch);
  }

  componentWillUnmount() {
    document.removeEventListener('login-success', this.handeSearch);
  }

  handeSearch(value) {
    const { detail: { response } = {} } = value || {};
    this.props.loginSuccess(response);
  }

  render() {
    return (
      <div>
        {/* General layout for all auth pages, such as forgot password */}
        <Switch>
          {/* V2 LOGIN - OTP BASED */}
          <Route
            path={loginPath}
            exact
            render={() => (
              <div className="login-wrapper">
                <login-module
                  onSuccess={this.props.loginSuccess}
                  app={this.props.systemType}
                  strEncrPsw
                  authURL={'/auth/v2'}
                  domain={crmDevServer ? 'localhost' : 'paytmmoney.com'}
                  cookiesConfig={JSON.stringify(COOKIES_CONFIG)}
                />
              </div>
            )}
          />
          <Redirect to={loginPath} />
        </Switch>
      </div>
    );
  }
}

const mapDispatchToProps = (dispatch) => ({
  loginSuccess: bindActionCreators(loginSuccess, dispatch),
});

export default connect(
  null,
  mapDispatchToProps,
)(UnauthorizedLayout);
