import React from 'react';
import className from 'classnames';
import isEmpty from 'lodash/isEmpty';
import { NavLink } from 'react-router-dom';
import PropTypes from 'prop-types';
import Tooltip from 'rc-tooltip';

import { PtmNavTabs } from '@paytm-money/frontend-common-v2';
import { customerDetailsNavIds, CUSTOMER_DETAILS_NAV, CUSTOMER_DETAILS_TAB } from '../nav-config';

import { getMatchedPermissions } from '../../../utilities/permission';

import NavigationScroll from '../../../common/navigation-scroll/navigation-scroll';

import '../../../../styles/customer-details.scss';
import '../../../../styles/helper.scss';

const CustomerDetailsNav = (props) => {
  const {
    match: { path },
    selectedCustomerId,
    fieldApprovalMeta,
    equityFieldApprovalMeta,
    incidentMeta,
    paymentModeMeta,
    selectedCustomerTabId,
    disableTabFlag,
    ...rest
  } = props;
  // Overriding the title and adding meta info to be displayed in the tab text
  const options = getMatchedPermissions({
    props,
    configArray: CUSTOMER_DETAILS_NAV,
  });

  const keysArr = Object.keys(CUSTOMER_DETAILS_TAB);

  const selectedTab = options.find((x) => x.id === selectedCustomerTabId);

  const selectedTabId = selectedTab ? selectedTab.parentTabId : 1;

  const navList = [];
  keysArr.map((key) => {
    const navObj = CUSTOMER_DETAILS_TAB[key];
    const tabNav = options.filter((i) => i.parentTabId === navObj.id);
    if (navObj.active && tabNav.length) {
      const defaultTab = tabNav.length ? tabNav[0] : {};
      navObj.routeUrl = `${path}/${selectedCustomerId}${defaultTab.navPath}`;
    }
    navList.push(navObj);
  });

  const navProps = {
    textExtStyle: 'h4 push-10-l text-muted-1',
    activeStyle: 'selected-tab',
    navList,
  };
  if (!isNaN(parseInt(selectedTabId, Number))) {
    navProps.activeIndex = selectedTabId - 1;
  }
  const temp = JSON.parse(JSON.stringify(options));

  const configArray = temp.filter((item) => item.parentTabId === selectedTabId).map((navObj) => {
    const tempObj = { ...navObj };
    tempObj.path = `${path}/${selectedCustomerId}${tempObj.navPath}`;
    if (tempObj.id === customerDetailsNavIds.CORE_IR_READINESS && !isEmpty(fieldApprovalMeta)) {
      tempObj.title += ` (${fieldApprovalMeta.pending}/${fieldApprovalMeta.total})`;
    }

    if (tempObj.id === customerDetailsNavIds.EQUITY_IR_READINESS && !isEmpty(equityFieldApprovalMeta)) {
      tempObj.title += ` (${equityFieldApprovalMeta.pending}/${equityFieldApprovalMeta.total})`;
    }

    if (tempObj.id === customerDetailsNavIds.FORMS && !isEmpty(incidentMeta)) {
      tempObj.title += ` (${incidentMeta.pending}/${incidentMeta.total})`;
    }

    if (tempObj.id === customerDetailsNavIds.EQUITY_IR_READINESS && disableTabFlag.EQUITY) {
      tempObj.isDisabled = true;
    }

    if (tempObj.id === customerDetailsNavIds.NPS_IR_READINESS && disableTabFlag.NPS) {
      tempObj.isDisabled = true;
    }

    if (tempObj.id === customerDetailsNavIds.NFT_APPROVAL && disableTabFlag.NFT) {
      tempObj.isDisabled = true;
    }

    return tempObj;
  });

  return (
    <div className="flex-direction-column flex-container">
      <div className="flex-container tab-navigator">
        <PtmNavTabs {...navProps} />
      </div>
      <div className="flex-container padding-0">
        <NavigationScroll
          configArray={configArray}
          selectedTabId={selectedCustomerTabId}
          navComponent={NavigationButton}
          width={250}
          {...rest}
        />
      </div>
    </div>
  );
};

const returnTrimmedTitle = (title) => {
  if (title.length > 25) {
    const trimmedString = `${title.substring(0, 24)}...`;
    return (
      <Tooltip
        placement="top"
        overlay={title}
        overlayClassName="info-tooltip"
      >
        <span>{trimmedString}</span>
      </Tooltip>
    );
  }
  return (
    <span>{title}</span>
  );
};

const NavigationButton = (props) => {
  const {
    iconClass, isSelected, navObj,
  } = props;

  const { title, path } = navObj;
  const contentDisplayClass = className({
    'text-white': isSelected,
    'text-muted-1': !isSelected,
  });
  const containerClass = className({
    'selected-tab': isSelected,
  });

  const handleClick = (e) => {
    if (navObj.isDisabled) { e.preventDefault(); }
  };

  return (
    <NavLink to={`${path}`} onClick={handleClick}>
      <div
        role="button"
        className={`block-content block-content-full ${containerClass} clearfix`}
      >
        {/* <i className={`${iconClass} fa-2x pull-left ${contentDisplayClass}`} /> */}
        <span className={`h4 push-10-l ${contentDisplayClass}`}>{returnTrimmedTitle(title)}</span>
      </div>
    </NavLink>
  );
};

CustomerDetailsNav.propTypes = {
  match: PropTypes.object.isRequired,
  fieldApprovalMeta: PropTypes.object,
  equityFieldApprovalMeta: PropTypes.object,
  incidentMeta: PropTypes.object,
  paymentModeMeta: PropTypes.object,
  selectedCustomerId: PropTypes.number.isRequired,
  selectedCustomerTabId: PropTypes.number.isRequired,
  disableTabFlag: PropTypes.object,
};

CustomerDetailsNav.defaultProps = {
  fieldApprovalMeta: {},
  equityFieldApprovalMeta: {},
  incidentMeta: {},
  paymentModeMeta: {},
  disableTabFlag: {},
};

NavigationButton.propTypes = {
  path: PropTypes.string,
  title: PropTypes.string,
  iconClass: PropTypes.string,
  isSelected: PropTypes.bool.isRequired,
};

NavigationButton.defaultProps = {
  path: '',
  title: '',
  iconClass: '',
};

// For testing purpose
NavigationButton.dispayName = 'NavigationButton';

export default CustomerDetailsNav;
