import React from 'react';
import PropTypes from 'prop-types';
import '../../../../styles/customer-details.scss';
import '../../../../styles/helper.scss';
import CustomerDetailsNav from './CustomerDetailsNav';
import Loader from '../../../common/Loader';
import { PRODUCT_TYPES } from '../nav-config';
import ApiService from '../../../../services/ApiService';
import { ENDPOINTS } from '../../../../constants/api-constants';

const COLOR_CODE = {
  VERIFIED: 'color-green',
  ACTIVE: 'color-green',
  PENDING: 'color-yellow',
  REVOKED: 'color-red',
};
const COLOR_CODE_RATING = {
  'A++': 'color-aplusplus',
  'A+': 'color-aplus',
  A: 'color-a',
  B: 'color-b',
  '-': 'color-nograde',
};

const USER_SEGMENT_COLORS = {
  A: 'color-green',
  B: 'color-blue',
  C: 'color-yellow',
  D: 'color-red',
  E: 'color-grey',
};

const initialBackOfficeDetails = {
  ucc : "NA",
  boid : "NA"
}
const CustomerDetailsHeader = (props) => {
  const {
    customerDetails,
    fieldApprovalMeta,
    equityFieldApprovalMeta,
    oneTimeMandateMeta,
    investmentReadinessStatus,
    disableTabFlag,
    productRating,
  } = props;
  const {
    match,
    selectedCustomerId,
    incidentMeta,
    paymentModeMeta,
    selectedCustomerTabId,
    permissions,
    // userType: type,
    ...rest
  } = props;
  const [status, setStatus] = React.useState(null);
  const [backOffice, setBackOffice] = React.useState(initialBackOfficeDetails);
  const [profile, setProfile] = React.useState(null);
  const [kraStatus, setKraStatus] = React.useState(null);
  const [userSegment, setUserSegment] = React.useState(null);

  React.useEffect(() => {
    ApiService.makeRequest(ENDPOINTS.customer.isPriorityCustomer, null, selectedCustomerId)
      .then((response) => {
        const { isPriorityCustomer, segmentInfo } = response || {};
        setStatus(isPriorityCustomer);
        if (segmentInfo) {
          setUserSegment(segmentInfo.segment || null);
        }
      })
      .catch(() => setStatus(null));
    ApiService.makeRequest(ENDPOINTS.customer.getCvlStatus, { updateValue: false }, selectedCustomerId)
      .then((response) => setKraStatus(response.cvlActualStatus))
      .catch(() => setKraStatus(null));
    ApiService.makeRequest(ENDPOINTS.customer.customerPlans, null, selectedCustomerId)
      .then((response) => setProfile(response.brokerageProfile))
      .catch(() => setProfile(null));
    ApiService.makeRequest(ENDPOINTS.customer.getBackOfficeDetailsForHeader, null, selectedCustomerId)
    .then((response) => response.data ? setBackOffice(response.data) : setBackOffice(initialBackOfficeDetails))
    .catch(() => setBackOffice(initialBackOfficeDetails));
  }, [selectedCustomerId]);
  return (
    <div className="">
      {customerDetails ? (
      <div className="flex-container flex-start">
        <div className="flex-container investor-info padding-0 flex-start flex-30">
          <img className="img-avatar img-avatar-thumb" src={customerDetails.investorProfileImg} alt="" />
          <div>
            <h2 className="text-white text-capitalize push-5-t">{customerDetails.name}</h2>
            <div className="customer-details-container">
              <div className="customer-details-box">
                {(productRating || {}).REKYC && (
                    <div className='header-info'>Re Kyc: {(productRating || {}).REKYC}</div> 
                )}
                <div className="header-info-contaniner">
                  <div>
                    <div className='header-info'>
                      Cust Id:
                      {' '}
                      {selectedCustomerId}
                    </div>
                    <div className='header-info'>
                      Brokerage Code:
                      <br />
                      {profile}
                    </div>
                  </div>
                  <div>
                  <div className='header-info'>
                    Equity UCC:
                    {' '}
                    {backOffice.ucc}
                  </div>
                  <div className='header-info'>
                    BO ID: 
                    {' '}
                    {backOffice.boid}
                  </div>
                    <div className='header-info'>
                      KRA:
                      {' '}
                      {kraStatus}
                  </div>
                  </div>
              </div>
              </div>
            </div>
          </div>
        </div>
        <div className="flex-container flex-start padding-0 flex-60 align-flex-start">
          {
            PRODUCT_TYPES.map((product, key) => (
              <div key={`${product}_${key}`} className="flex-20 pad-10">
                <h4 className="push-5">{product.title}</h4>
                <div className={`text-bold ${COLOR_CODE[investmentReadinessStatus[product.keyValue]] || 'color-red'}`}>{investmentReadinessStatus[product.keyValue]}</div>
                {productRating ? (
                  <div className={`text-bold product-rating ${COLOR_CODE_RATING[productRating[product.keyValue]] || 'color-red'}`}>
                    <h4 className="product-rating-text">{productRating[product.keyValue]}</h4>
                  </div>
                ) : (null)}
              </div>
            ))
          }
          {userSegment ? (
            <div className="flex-20 pad-10">
              <h4 className="push-5">User segment</h4>
              <div className={`text-bold ${USER_SEGMENT_COLORS[userSegment]}`}>{userSegment}</div>
            </div>
          ) : null}
        </div>

      </div>
      ) : (
        <Loader.SectionLoader />
      )}
      {/* // <hr /> */}
      <CustomerDetailsNav
          // userType={type}
        match={match}
        selectedCustomerId={selectedCustomerId}
        selectedCustomerTabId={selectedCustomerTabId}
        fieldApprovalMeta={fieldApprovalMeta}
        equityFieldApprovalMeta={equityFieldApprovalMeta}
        oneTimeMandateMeta={oneTimeMandateMeta}
        incidentMeta={incidentMeta}
        paymentModeMeta={paymentModeMeta}
        permissions={permissions}
        disableTabFlag={disableTabFlag}
      />
    </div>
  );
};

CustomerDetailsHeader.propTypes = {
  customerDetails: PropTypes.object,
  investmentReadinessStatus: PropTypes.object.isRequired,
  fieldApprovalMeta: PropTypes.object,
  equityFieldApprovalMeta: PropTypes.object,
  oneTimeMandateMeta: PropTypes.object.isRequired,
  productRating: PropTypes.object,
};

// TODO: Replace with the placeholder url
CustomerDetailsHeader.defaultProps = {
  customerDetails: null,
  fieldApprovalMeta: null,
  equityFieldApprovalMeta: null,
  productRating: null,
};

export default CustomerDetailsHeader;
