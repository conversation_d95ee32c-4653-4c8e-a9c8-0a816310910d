import EqMarginPledgeLayout from "../../pages/customer-details/eq-margin-pledge/EqMarginPledge";

const customerDetailsTabIds = {
  ON_BOARDING: 1,
  MUTUAL_FUND: 2,
  NPS: 3,
  EQUITY_FUND: 4,
  CUSTOMER_REFFERAL: 5,
  CAMPAIGNS: 6,
};

const customerDetailsTabActiveFlag = {
  ON_BOARDING: 1,
  MUTUAL_FUND: 1,
  NPS: 1,
  EQUITY_FUND: 1,
  CUSTOMER_REFFERAL: 1,
  CAMPAIGNS: 1,
};

const PRODUCT_TYPES = [
  {
    title: 'Mutual Fund',
    keyValue: 'MF',
  }, {
    title: 'Equity (Cash)',
    keyValue: 'EQUITY',
  }, {
    title: 'FNO',
    keyValue: 'FNO',
  }, {
    title: 'NPS',
    keyValue: 'NPS',
  },
];

const CUSTOMER_DETAILS_TAB = {
  ON_BOARDING: {
    id: customerDetailsTabIds.ON_BOARDING,
    title: 'Onboarding',
    active: customerDetailsTabActiveFlag.ON_BOARDING,
  },
  MUTUAL_FUND: {
    id: customerDetailsTabIds.MUTUAL_FUND,
    title: 'Mutual Funds',
    active: customerDetailsTabActiveFlag.ON_BOARDING,
  },
  NPS: {
    id: customerDetailsTabIds.NPS,
    title: 'NPS',
    active: customerDetailsTabActiveFlag.NPS,
  },
  EQUITY_FUND: {
    id: customerDetailsTabIds.EQUITY_FUND,
    title: 'Equity and F&O',
    active: customerDetailsTabActiveFlag.EQUITY_FUND,
  },
  CUSTOMER_REFFERAL: {
    id: customerDetailsTabIds.CUSTOMER_REFFERAL,
    title: 'Customer Data',
    active: customerDetailsTabActiveFlag.CUSTOMER_REFFERAL,
  },
  CAMPAIGNS: {
    id: customerDetailsTabIds.CAMPAIGNS,
    title: 'Campaigns',
    active: customerDetailsTabActiveFlag.CAMPAIGNS,
  },
};

const customerDetailsNavIds = {
  BASIC_DETAILS: 1,
  CORE_IR_READINESS: 2,
  EQUITY_IR_READINESS: 3,
  NPS_IR_READINESS: 4,
  PAYMENT_MODES: 5,
  PAYMENT_MODES_MF: 6,
  FORMS: 7,
  PORTFOLIO: 8,
  EXTERNAL_PORTFOLIO: 9,
  ACTIVE_SIPS: 10,
  CANCELLED_SIPS: 12,
  TRANSACTIONS: 11,
  STATEMENTS: 13,
  NPS_PORTFOLIO: 14,
  NPS_ACTIVE_SIPS: 15,
  NPS_TRANSACTIONS: 16,
  NPS_INCIDENTS_USER: 17,
  NPS_STATEMENTS: 18,
  EQ_ORDERS: 19,
  EQ_POSITIONS: 20,
  EQ_CHARGES: 21,
  EQ_PORTFOLIO: 22,
  EQ_FUNDS: 23,
  EQ_PNL: 24,
  EQ_STATEMENTS_AND_FORMS: 25,
  EQ_EPF_DETAILS: 26,
  NFT_APPROVAL: 27,
  PAYMENT_MODES_EQUITY: 28,
  EQ_IPO_APPLICATIONS: 29,
  USER_FEEDBACK: 30,
  EQ_MARGIN_PLEDGE: 31,
  EQ_HNI_IPO_APPLICATIONS: 32,
  EQ_WEALTHDESK_PORTFOLIO: 33,
  REFFERAL_SOURCE: 34,
  ACTIVE_CAMPAIGNS: 35,
  EQ_BUYBACK: 36,
  CUSTOMER_REWARDS: 37,
  DEMAT_HOLDINGS: 38,
};

const CUSTOMER_DETAILS_NAV = {

  // Onboarding
  BASIC_DETAILS: {
    id: customerDetailsNavIds.BASIC_DETAILS,
    navPath: '/basic-details',
    title: 'Basic Details',
    iconClass: 'si si-speedometer',
    permissions: ['BASIC_DETAILS'],
    parentTabId: customerDetailsTabIds.ON_BOARDING,
  },
  CORE_IR_READINESS: {
    id: customerDetailsNavIds.CORE_IR_READINESS,
    navPath: '/core-ir-readiness',
    title: 'Core IR Readiness',
    iconClass: 'si si-rocket',
    permissions: ['INVESTMENT_READINESS'],
    parentTabId: customerDetailsTabIds.ON_BOARDING,
  },
  EQUITY_IR_READINESS: {
    id: customerDetailsNavIds.EQUITY_IR_READINESS,
    navPath: '/equity-ir-readiness',
    title: 'Equity IR Readiness',
    iconClass: 'si si-rocket',
    permissions: ['INVESTMENT_READINESS'],
    parentTabId: customerDetailsTabIds.ON_BOARDING,
  },
  NPS_IR_READINESS: {
    id: customerDetailsNavIds.NPS_IR_READINESS,
    navPath: '/nps-ir-readiness',
    title: 'NPS IR Readiness',
    iconClass: 'si si-rocket',
    permissions: ['INVESTMENT_READINESS'],
    parentTabId: customerDetailsTabIds.ON_BOARDING,
  },
  USER_FEEDBACK: {
    id: customerDetailsNavIds.USER_FEEDBACK,
    navPath: '/user-feedback',
    title: 'User Feedback',
    iconClass: 'si si-rocket',
    permissions: ['USER_FEEDBACK'],
    parentTabId: customerDetailsTabIds.ON_BOARDING,
  },
  NFT_IR_APPROVAL: {
    id: customerDetailsNavIds.NFT_APPROVAL,
    navPath: '/nft-approval',
    title: 'NFT Approval',
    iconClass: 'si si-rocket',
    permissions: ['NFT_APPROVAL'],
    parentTabId: customerDetailsTabIds.ON_BOARDING,
  },
  PAYMENT_MODES: {
    id: customerDetailsNavIds.PAYMENT_MODES,
    navPath: '/payment-modes',
    title: 'Payment Modes',
    iconClass: 'si si-wallet',
    permissions: ['PAYMENT_MODES'],
    parentTabId: customerDetailsTabIds.ON_BOARDING,
  },
  FORMS: {
    id: customerDetailsNavIds.FORMS,
    navPath: '/forms',
    title: 'Forms',
    iconClass: 'si si-folder-alt',
    permissions: ['FORMS'],
    parentTabId: customerDetailsTabIds.ON_BOARDING,
  },

  // MF
  PORTFOLIO: {
    id: customerDetailsNavIds.PORTFOLIO,
    navPath: '/portfolio',
    title: 'Portfolio',
    iconClass: 'si si-folder-alt',
    permissions: ['PORTFOLIO'],
    parentTabId: customerDetailsTabIds.MUTUAL_FUND,
  },
  DEMAT_HOLDINGS: {
    id: customerDetailsNavIds.DEMAT_HOLDINGS,
    navPath: '/demat-holdings',
    title: 'Demat Holdings',
    iconClass: 'si si-briefcase',
    permissions: ['PORTFOLIO'],
    parentTabId: customerDetailsTabIds.MUTUAL_FUND,
  },
  PAYMENT_MODES_MF: {
    id: customerDetailsNavIds.PAYMENT_MODES_MF,
    navPath: '/payment-modes-mf',
    title: 'Payment Modes',
    iconClass: 'si si-wallet',
    permissions: ['PAYMENT_MODES'],
    parentTabId: customerDetailsTabIds.MUTUAL_FUND,
  },
  EXTERNAL_PORTFOLIO: {
    id: customerDetailsNavIds.EXTERNAL_PORTFOLIO,
    navPath: '/external-portfolio',
    title: 'External Portfolio',
    iconClass: 'si si-folder-alt',
    permissions: ['EXTERNAL_PORTFOLIO'],
    parentTabId: customerDetailsTabIds.MUTUAL_FUND,
  },
  ACTIVE_SIPS: {
    id: customerDetailsNavIds.ACTIVE_SIPS,
    navPath: '/active-sip',
    title: 'Active SIPs',
    iconClass: 'si si-loop',
    permissions: ['ACTIVE_SIPS'],
    parentTabId: customerDetailsTabIds.MUTUAL_FUND,
  },
  CANCELLED_SIPS: {
    id: customerDetailsNavIds.CANCELLED_SIPS,
    navPath: '/cancelled-sip',
    title: 'Cancelled SIPs',
    iconClass: 'si si-loop',
    permissions: ['ACTIVE_SIPS'],
    parentTabId: customerDetailsTabIds.MUTUAL_FUND,
  },
  TRANSACTIONS: {
    id: customerDetailsNavIds.TRANSACTIONS,
    navPath: '/transactions',
    title: 'Transactions',
    iconClass: 'si si-directions',
    permissions: ['TRANSACTIONS'],
    parentTabId: customerDetailsTabIds.MUTUAL_FUND,
  },
  STATEMENTS: {
    id: customerDetailsNavIds.STATEMENTS,
    navPath: '/statements',
    title: 'Statements',
    iconClass: 'si si-docs',
    permissions: ['STATEMENTS'],
    parentTabId: customerDetailsTabIds.MUTUAL_FUND,
  },

  NPS_PORTFOLIO: {
    id: customerDetailsNavIds.NPS_PORTFOLIO,
    navPath: '/nps-portfolio',
    title: 'Portfolio',
    iconClass: 'si si-folder-alt',
    permissions: ['PORTFOLIO'],
    parentTabId: customerDetailsTabIds.NPS,
  },
  NPS_ACTIVE_SIPS: {
    id: customerDetailsNavIds.NPS_ACTIVE_SIPS,
    navPath: '/nps-active-sip',
    title: 'Active SIPs',
    iconClass: 'si si-loop',
    permissions: ['ACTIVE_SIPS'],
    parentTabId: customerDetailsTabIds.NPS,
  },
  NPS_TRANSACTIONS: {
    id: customerDetailsNavIds.NPS_TRANSACTIONS,
    navPath: '/nps-transactions',
    title: 'Transactions',
    iconClass: 'si si-directions',
    permissions: ['TRANSACTIONS'],
    parentTabId: customerDetailsTabIds.NPS,
  },
  NPS_STATEMENTS: {
    id: customerDetailsNavIds.NPS_STATEMENTS,
    navPath: '/nps-statements',
    title: 'Statements',
    iconClass: 'si si-docs',
    permissions: ['STATEMENTS'],
    parentTabId: customerDetailsTabIds.NPS,
  },

  // EF
  EQ_ORDERS: {
    id: customerDetailsNavIds.EQ_ORDERS,
    navPath: '/eq-orders',
    title: 'Orders',
    iconClass: 'si si-folder-alt',
    permissions: ['EQ_ORDERS'],
    parentTabId: customerDetailsTabIds.EQUITY_FUND,
  },
  EQ_POSITIONS: {
    id: customerDetailsNavIds.EQ_POSITIONS,
    navPath: '/eq-positions',
    title: 'Positions',
    iconClass: 'si si-folder-alt',
    permissions: ['EQ_POSITIONS'],
    parentTabId: customerDetailsTabIds.EQUITY_FUND,
  },
  PAYMENT_MODES_EQUITY: {
    id: customerDetailsNavIds.PAYMENT_MODES_EQUITY,
    navPath: '/payment-modes-equity',
    title: 'Payment Modes',
    iconClass: 'si si-wallet',
    permissions: ['PAYMENT_MODES'],
    parentTabId: customerDetailsTabIds.EQUITY_FUND,
  },
  EQ_CHARGES: {
    id: customerDetailsNavIds.EQ_CHARGES,
    navPath: '/eq-charges',
    title: 'Charges',
    iconClass: 'si si-loop',
    permissions: ['EQ_CHARGES'],
    parentTabId: customerDetailsTabIds.EQUITY_FUND,
  },
  EQ_PORTFOLIO: {
    id: customerDetailsNavIds.EQ_PORTFOLIO,
    navPath: '/eq-portfolio',
    title: 'Portfolio',
    iconClass: 'si si-directions',
    permissions: ['EQ_PORTFOLIO'],
    parentTabId: customerDetailsTabIds.EQUITY_FUND,
  },
  EQ_WEALTHDESK_PORTFOLIO: {
    id: customerDetailsNavIds.EQ_WEALTHDESK_PORTFOLIO,
    navPath: '/eq-wealthdesk-portfolio',
    title: 'Channel Partner Portolio',
    iconClass: 'si si-directions',
    permissions: ['EQ_PORTFOLIO'],
    parentTabId: customerDetailsTabIds.EQUITY_FUND,
  },
  EQ_FUNDS: {
    id: customerDetailsNavIds.EQ_FUNDS,
    navPath: '/eq-funds',
    title: 'Funds',
    iconClass: 'si si-pin',
    permissions: ['EQ_FUNDS'],
    parentTabId: customerDetailsTabIds.EQUITY_FUND,
  },
  EQ_EPF_DETAILS: {
    id: customerDetailsNavIds.EQ_EPF_DETAILS,
    navPath: '/epf-details',
    title: 'EPF Info',
    parentTabId: customerDetailsTabIds.MUTUAL_FUND,
    permissions: ['EPF_DETAILS'],
  },
  // EQ_PNL: {
  //   id: customerDetailsNavIds.EQ_PNL,
  //   navPath: '/eq-pnl',
  //   title: 'PNL',
  //   iconClass: 'si si-pin',
  //   permissions: ['EQ_PNL'],
  //   parentTabId: customerDetailsTabIds.EQUITY_FUND,
  // },
  EQ_STATEMENTS_AND_FORMS: {
    id: customerDetailsNavIds.EQ_STATEMENTS_AND_FORMS,
    navPath: '/eq-statements-and-forms',
    title: 'Statements',
    iconClass: 'si si-docs',
    permissions: ['EQ_STATEMENT_FORM'],
    parentTabId: customerDetailsTabIds.EQUITY_FUND,
  },
  EQ_IPO_APPLICATIONS: {
    id: customerDetailsNavIds.EQ_IPO_APPLICATIONS,
    navPath: '/eq-ipo-applications',
    title: 'IPO Applications',
    iconClass: 'si si-docs',
    permissions: ['EQ_ORDERS'],
    parentTabId: customerDetailsTabIds.EQUITY_FUND,
  },
  EQ_HNI_IPO_APPLICATIONS: {
    id: customerDetailsNavIds.EQ_HNI_IPO_APPLICATIONS,
    navPath: '/eq-ipo-hni-applications',
    title: 'IPO HNI Applications',
    iconClass: 'si si-docs',
    permissions: ['EQ_ORDERS'],
    parentTabId: customerDetailsTabIds.EQUITY_FUND,
  },
  EQ_SUBSCRIPTION: {
    id: customerDetailsNavIds.EQ_SUBSCRIPTION,
    navPath: '/eq-subscription',
    title: 'Subscription',
    iconClass: 'si si-docs',
    permissions: ['SUBSCRIPTION_READ', 'SUBSCRIPTION_WRITE'],
    parentTabId: customerDetailsTabIds.EQUITY_FUND,
  },
  EQ_MARGIN_PLEDGE: {
    id: customerDetailsNavIds.EQ_MARGIN_PLEDGE,
    permissions: ['EQ_ORDERS'],
    navPath: '/eq-margin-pledge',
    title: 'Margin Pledge',
    iconClass: 'si si-docs',
    parentTabId: customerDetailsTabIds.EQUITY_FUND,
  },
  EQ_BUYBACK: {
    id: customerDetailsNavIds.EQ_BUYBACK,
    permissions: ['EQ_ORDERS'],
    navPath: '/eq-buyback',
    title: 'Buybacks',
    iconClass: 'si si-docs',
    parentTabId: customerDetailsTabIds.EQUITY_FUND,
  },
  // Customer refferal
  REFFERAL_SOURCE: {
    id: customerDetailsNavIds.REFFERAL_SOURCE,
    permissions: ['EQ_ORDERS'],
    navPath: '/customer-refferal',
    title: 'Referral Source',
    iconClass: 'si si-docs',
    parentTabId: customerDetailsTabIds.CUSTOMER_REFFERAL,
  },

  REWARDS_DATA: {
    id: customerDetailsNavIds.CUSTOMER_REWARDS,
    permissions: ['EQ_ORDERS'],
    navPath: '/customer-rewards-data',
    title: 'Rewards Data',
    iconClass: 'si si-docs',
    parentTabId: customerDetailsTabIds.CUSTOMER_REFFERAL,
  },

  // Campaigns
  ACTIVE_CAMPAIGNS: {
    id: customerDetailsNavIds.ACTIVE_CAMPAIGNS,
    permissions: ['BASIC_DETAILS'],
    navPath: '/active-campaigns',
    title: 'Active Campaigns',
    iconClass: 'si si-docs',
    parentTabId: customerDetailsTabIds.CAMPAIGNS,
  },
};

export {
  customerDetailsTabIds,
  CUSTOMER_DETAILS_TAB,
  customerDetailsNavIds,
  PRODUCT_TYPES,
  CUSTOMER_DETAILS_NAV,
};
