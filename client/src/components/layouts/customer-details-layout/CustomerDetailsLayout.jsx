import React, { Component } from 'react';
import PropTypes from 'prop-types';
import merge from 'lodash/merge';
import { connect } from 'react-redux';
import { PtmToast } from '@paytm-money/frontend-common-v2';
import Loader from '../../common/Loader';
import CustomerDetailsHeader from './partials/CustomerDetailsHeader';
import CustomerDetailsRouter from '../../routers/customer-details-router';

import { fetchBasicDetails, resetCustomerDetailsData, fetchInvestmentDetails } from '../../../actions/customer-details-actions';
import { updateSelectedCustomerTab } from '../../../actions/navigation-actions';

export class CustomerDetailsLayout extends Component {
  // Extract customer Id from url if the customer id is not a number then return null
  static extractCustomerId(pathname) {
    const custId = parseInt(pathname.split('/')[2], 10);
    if (Number.isNaN(custId)) {
      return null;
    }
    return custId;
  }

  constructor(props) {
    super(props);
    const { location: { pathname } } = props;
    this.selectedCustomerId = CustomerDetailsLayout.extractCustomerId(pathname);
  }

  componentDidMount() {
    if (this.selectedCustomerId) {
      this.props.fetchBasicDetails(this.selectedCustomerId);
    }
  }

  componentWillReceiveProps(nextProps) {
    const { location: { pathname } } = nextProps;
    const updatedCustId = CustomerDetailsLayout.extractCustomerId(pathname);
    if (updatedCustId && (this.selectedCustomerId !== updatedCustId)) {
      this.selectedCustomerId = updatedCustId;
      this.props.fetchBasicDetails(updatedCustId);
    }
  }

  componentWillUnmount() {
    this.props.resetCustomerDetailsData();
  }

  render() {
    const {
      selectedCustomerTabId,
      customerInfoMeta,
      fieldApprovalMeta,
      equityFieldApprovalMeta,
      oneTimeMandateMeta,
      incidentMeta,
      paymentModeMeta,
      updateSelectedCustomerNav,
      isFetching,
      error,
      customerDetails,
      match,
      location,
      history,
      permissions,
      kycDownloadInfo,
      epfDetails,
      deviceInfo,
      modificationsEmailInfo,
      productRating,
      userId
    } = this.props;
    const { selectedCustomerId } = this;

    if (!selectedCustomerId) {
      return (
        <PtmToast
          message="Invalid customer id"
          isVisible
        />
      );
    }

    if (error) {
      return (
        <PtmToast
          message={error}
          isVisible={Boolean(error)}
        />
      );
    }

    return (
      <main id="main-container" style={{ backgroundColor: 'transparent' }}>
        <CustomerDetailsHeader
          customerDetails={customerDetails}
          investorName={customerDetails ? customerDetails.name : null}
          investorProfileImgUrl={customerDetails ? customerDetails.investorProfileImg : null}
          {...customerInfoMeta}
          fieldApprovalMeta={fieldApprovalMeta}
          equityFieldApprovalMeta={equityFieldApprovalMeta}
          oneTimeMandateMeta={oneTimeMandateMeta}
          match={match}
          selectedCustomerId={selectedCustomerId}
          selectedCustomerTabId={selectedCustomerTabId}
          incidentMeta={incidentMeta}
          paymentModeMeta={paymentModeMeta}
          permissions={permissions}
          productRating={productRating}
        />
        <div className="content content-full">
          <CustomerDetailsRouter
            updateNavLinkFn={updateSelectedCustomerNav}
            match={match}
            location={location}
            history={history}
            selectedCustomerId={selectedCustomerId}
            customerDetails={{
              customerDetails, kycDownloadInfo, epfDetails, selectedCustomerId, deviceInfo, modificationsEmailInfo
            }}
            permissions={permissions}
            userId={userId}
            {...customerInfoMeta}
          />
        </div>
      </main>
    );
  }
}

CustomerDetailsLayout.propTypes = {
  fetchBasicDetails: PropTypes.func.isRequired,
  fetchEquityRelatedInfo: PropTypes.func.isRequired,
  updateSelectedCustomerNav: PropTypes.func.isRequired,
  selectedCustomerTabId: PropTypes.number.isRequired,
  resetCustomerDetailsData: PropTypes.func.isRequired,
  match: PropTypes.object.isRequired,
  customerInfoMeta: PropTypes.object,
  isFetching: PropTypes.bool,
  customerDetails: PropTypes.object,
  history: PropTypes.object.isRequired,
  error: PropTypes.string,
  incidentMeta: PropTypes.object,
  paymentModeMeta: PropTypes.object,
  fieldApprovalMeta: PropTypes.object,
  equityFieldApprovalMeta: PropTypes.object,
  oneTimeMandateMeta: PropTypes.object,
  location: PropTypes.object.isRequired,
  permissions: PropTypes.array.isRequired,
  kycDownloadInfo: PropTypes.object.isRequired,
  epfDetails: PropTypes.object.isRequired,
  deviceInfo: PropTypes.array,
  modificationsEmailInfo: PropTypes.array,
  // userType: PropTypes.oneOf(Object.keys(UserType)).isRequired,
  productRating: PropTypes.object,
};

CustomerDetailsLayout.defaultProps = {
  customerInfoMeta: null,
  paymentModeMeta: null,
  fieldApprovalMeta: null,
  equityFieldApprovalMeta: null,
  oneTimeMandateMeta: null,
  incidentMeta: null,
  error: null,
  isFetching: false,
  customerDetails: null,
  deviceInfo: [],
  modificationsEmailInfo: [],
  productRating: null,
};

const mapStateToProps = (state) => {
  const {
    customerDetails: {
      basicDetailsData,
      approvalModal,
      riskProfile,
      ...rest
    },
    navigation: { selectedCustomerTabId },
    auth: { loggedInUser: { permissions } },
  } = state;
  const updatedDetailsData = basicDetailsData
    ? merge(basicDetailsData, { riskProfile: { ...riskProfile } }) : null;
  return {
    permissions,
    customerDetails: updatedDetailsData,
    selectedCustomerTabId,
    ...rest,
  };
};

const mapDispatchToProp = (dispatch) => ({
  updateSelectedCustomerNav: (id) => dispatch(updateSelectedCustomerTab(id)),
  fetchBasicDetails: (userId) => dispatch(fetchBasicDetails(userId)),
  fetchInvestmentDetails: (userId) => dispatch(fetchInvestmentDetails(userId)),
  resetCustomerDetailsData: () => dispatch(resetCustomerDetailsData()),
});

export default connect(mapStateToProps, mapDispatchToProp)(CustomerDetailsLayout);
