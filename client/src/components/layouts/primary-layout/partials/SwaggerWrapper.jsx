import React from 'react';
import { PtmSwagger } from '@paytm-money/frontend-common-v2';
import React<PERSON>son from 'react-json-view';

import Loader from '../../../common/Loader';
import ApiService from '../../../../services/ApiService';
import { ENDPOINTS } from '../../../../constants/api-constants';
import ErrorSection from '../../../common/Error';

const JsonWrapper = ({ src }) => (
  <ReactJson src={src} theme="monokai" />
);

const SwaggerWrapper = () => {
  const [isLoading, setIsLoading] = React.useState(true);
  const [error, setErrorDetails] = React.useState({ show: false, message: '' });
  const [config, setConfig] = React.useState(null);

  React.useEffect(() => {
    ApiService.makeRequest(ENDPOINTS.swagger.fetchApiList)
      .then((response) => setConfig(response))
      .catch((errorObj) => setErrorDetails({ show: true, message: errorObj.error || 'Unable to fetch the list' }))
      .finally(() => setIsLoading(false));
  }, []);

  if (isLoading) {
    return (<Loader.PageLoader />);
  }

  if (error.show) {
    return <ErrorSection errorMsg={error.message} />;
  }

  return (
    <div>
      <PtmSwagger config={config} aggregatorUrl="/knowledge-api/microserviceList" MicroViewModel={JsonWrapper} />
    </div>
  );
};

export default SwaggerWrapper;
