import React from 'react';
import PropTypes from 'prop-types';

import { PtmNavTabs } from '@paytm-money/frontend-common-v2';

import '../../../../styles/header.css';
import { SWAGGER_PERMISSION } from '../nav-config';

const Navigator = (props) => {
  const {
    headerLogoPath,
    navConfig,
    activeComponentId,
    permissions,
  } = props;
  const navList = navConfig.map(({
    navigationPath, label, iconClass, subNavConfig,
  }) => {
    let subNavObj;
    if (subNavConfig) {
      const subNavList = subNavConfig.map(({ navigationPath, label }) => ({
        routeUrl: navigationPath,
        title: label,
      }));
      subNavObj = {
        activeIndex: -1,
        align: 'vertical',
        textExtStyle: 'sidebar-mini-hide',
        navList: subNavList,
        tabExtStyles: 'nav-tab-style',
        activeStyle: 'active',
      };
    }
    return {
      routeUrl: navigationPath,
      title: label,
      prevIcons: [iconClass],
      subNavObj,
    };
  });
  const navTabProps = {
    navList,
    activeIndex: parseInt(activeComponentId, Number) - 1,
    align: 'vertical',
    textExtStyle: 'sidebar-mini-hide',
    containerExtStyle: 'nav-main primary-nav',
    tabExtStyles: 'nav-tab-style',
    subNavExtStyle: 'open',
    subNavExtItemStyle: 'sub-nav',
    activeStyle: 'active',
  };

  return (
    <nav id="sidebar" style={{ zIndex: '90' }}>
      <div id="sidebar-scroll">
        <div className="sidebar-content">
          {
            headerLogoPath
              ? (
                <div className="side-header side-content bg-white-op">
                  <img className="logo-nav push-18" src={headerLogoPath} alt="Navigation" />
                </div>
              )
              : null
          }
          <div className="side-content side-content-full">
            <PtmNavTabs {...navTabProps} />
            {permissions.includes(SWAGGER_PERMISSION) && window.location.href.includes('preprod') && (
              <a className="swagger-link" href="/swagger-ui" target="_blank">
                <i className="fa fa-home" />
                <span>Swagger</span>
              </a>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigator;

Navigator.propTypes = {
  navConfig: PropTypes.array.isRequired,
  toggleSubmenu: PropTypes.func.isRequired,
  headerLogoPath: PropTypes.string,
  activeComponentId: PropTypes.number.isRequired,
  activeSubComponentId: PropTypes.number,
  openSubMenuIds: PropTypes.array,
  permissions: PropTypes.array.isRequired,
};

Navigator.defaultProps = {
  headerLogoPath: null,
  activeSubComponentId: null,
  openSubMenuIds: [],
};
