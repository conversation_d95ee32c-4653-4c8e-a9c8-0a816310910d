import React, { Component } from 'react';
import PropTypes from 'prop-types';
import Select from 'react-select';
import autobind from 'autobind-decorator';
import { NavLink } from 'react-router-dom';

import { PtmToast } from '@paytm-money/frontend-common-v2';
import SearchBar from '../../../common/search-bar';
import { validator } from '../../../utilities/validator';
import { fetchSearchResultsData } from '../../../../api-util';

import '../styles.scss';

// TODO: Break it into Container and Presentation Component
@autobind
class SearchHeader extends Component {
  static searchHeaderOptions = [
    { label: 'LogOut', value: 1 },
  ];

  constructor() {
    super();
    this.state = {
      isFetching: false,
      showToast: false,
      error: null,
    };
    this.searchFields = ['customerId', 'emailId', 'mobileNo'];
  }

  onSearch(searchTerm) {
    // handle selected customer id from the dropdown list
    if (!searchTerm) {
      return;
    }

    const matchedField = this.getFieldType(searchTerm);
    if (!matchedField) {
      alert(`Please enter a valid field.( ${this.searchFields.join(', ')} )`);
      return;
    }

    this.setState({ isFetching: true });
    fetchSearchResultsData(
      { [matchedField]: searchTerm },
      this.searchResultsSuccessCb,
      this.searchResultsErrorCb,
    );
  }

  getFieldType(searchTerm) {
    let matchedField;
    this.searchFields.forEach((field) => {
      if (validator(field, searchTerm).success) {
        matchedField = field;
      }
    });
    return matchedField;
  }

  searchResultsSuccessCb(response) {
    const { searchResults } = response;
    if (!searchResults.length) {
      this.setState({
        showToast: true,
        isFetching: false,
        error: 'User not found! Please enter valid credentials!!!',
      });
      return;
    }
    // Assuming only one reponse is returned
    const [firstResult] = searchResults;
    this.setState({ isFetching: false, error: null });
    this.props.history.push(`/customer-details/${firstResult.userId}`);
  }

  searchResultsErrorCb({ error }) {
    this.setState({ isFetching: false, showToast: true, error });
  }

  toggleToast(show) {
    this.setState({ showToast: show });
  }

  handleSelectChange(optn) {
    const { onLogout, userName } = this.props;
    if (optn.value === 1) {
      onLogout(userName);
    }
  }

  render() {
    const { userName, toggleNavBarDisplayAction } = this.props;
    const customStyles = {
      control: () => ({
        height: '28px',
        background: 'transparent',
        color: '#FFFFFF',
        border: '1px solid',
        borderRadius: '3px',
        display: 'flex',
      }),
      placeholder: () => ({
        color: '#fff',
      }),
    };
    return (
      <header id="header-navbar" className="content-mini content-mini-full" style={{ zIndex: 99, padding: '15px 10px 10px 10px' }}>
        <PtmToast
          isVisible={this.state.showToast}
          message={this.state.error}
          changeState={this.toggleToast}
        />
        {/* <!-- Header Navigation Right --> */}
        <div className="col-md-3 col-sm-3 pull-right">
          <Select
            styles={customStyles}
            placeholder={userName}
            options={SearchHeader.searchHeaderOptions}
            onChange={this.handleSelectChange}
          />
        </div>
        {/* <!-- END Header Navigation Right --> */}

        {/* <!-- Header Navigation Left --> */}
        <ul className="nav-header pull-left">
          <li className="hidden-md hidden-lg">
            <button
              className="btn btn-default"
              data-toggle="layout"
              data-action="sidebar_toggle"
              type="button"
            >
              <i className="fa fa-navicon" />
            </button>
          </li>
          <li className="hidden-xs hidden-sm">
            <i style={{ fontSize: '28px', color: '#FFFFFF' }} onClick={toggleNavBarDisplayAction} className="fa fa-ellipsis-v" />
          </li>
          <li className="js-header-search header-search">
            <SearchBar
              parentDivClass="col-lg-12 remove-margin-t remove-margin-b input-group input-header-search"
              submitBtnClass="btn-primary-dark"
              placeholderText="Enter Mobile Number/Email Id/User Id"
              onChange={this.onTextChange}
              shouldShowLoader={this.state.isFetching}
              onSearch={this.onSearch}
            />
          </li>
          <NavLink
            className="visible-lg-inline-block push-10-l push-5-t"
            to="/advanced-user-search"
          >
            Advanced Search
          </NavLink>
        </ul>
        {/* <!-- END Header Navigation Left --> */}
      </header>
    );
  }
}

SearchHeader.propTypes = {
  userName: PropTypes.oneOfType([PropTypes.string, PropTypes.object]).isRequired,
  onLogout: PropTypes.func.isRequired,
  history: PropTypes.any.isRequired,
  toggleNavBarDisplayAction: PropTypes.func.isRequired,
};

export default SearchHeader;
