import React from 'react';
import PropTypes from 'prop-types';
import Navigator from './Navigator';
import { PRIMARY_NAV } from '../nav-config';
import { getMatchedPermissions } from '../../../utilities/permission';

const PrimaryNavigation = (props) => {
  const {
    selectedTabId,
    selectedSubTabId,
    toggleSubmenu,
    openSubMenuIds,
    permissions,
  } = props;
  const options = {
    props,
    configArray: PRIMARY_NAV,
  };
  return (
    <Navigator
      navConfig={getMatchedPermissions(options)}
      headerLogoPath={'/static/assets/img/logo_white.svg'}
      activeComponentId={selectedTabId}
      activeSubComponentId={selectedSubTabId}
      toggleSubmenu={toggleSubmenu}
      openSubMenuIds={openSubMenuIds}
      permissions={permissions}
    />
  );
};

PrimaryNavigation.propTypes = {
  selectedTabId: PropTypes.number.isRequired,
  selectedSubTabId: PropTypes.number,
  toggleSubmenu: PropTypes.func.isRequired,
  openSubMenuIds: PropTypes.array.isRequired,
  permissions: PropTypes.array.isRequired,
};

PrimaryNavigation.defaultProps = {
  selectedSubTabId: null,
};

export default PrimaryNavigation;
