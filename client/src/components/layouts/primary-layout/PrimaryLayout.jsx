import React, { Component } from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import { bindActionCreators } from 'redux';
import '@paytm-money/frontend-common-v2/cdn/build/ptm-wrapper/ptm-wrapper';

import {
  updateSelectedTab,
  toggleNavBarDisplay,
  toggleSubmenu,
} from '../../../actions/navigation-actions';

import PrimaryRouter from '../../routers/primary-router';
import { logoutActionV2 } from '../../../actions/auth-actions';

import { PRIMARY_NAV, SWAGGER_PERMISSION } from './nav-config';

import PrimaryNavigation from './partials/PrimaryNavigation';
import SearchHeader from './partials/SearchHeader';
import '@paytm-money/frontend-common-v2/cdn/build/react-elements/esm/styles.css';
import SwaggerWrapper from './partials/SwaggerWrapper';

const RedirectURLKey = 'redirect-path=';

// Main layout for the application. Contains the search header, the page navigation component
// and the logic for selecting the page as per the route.
export class PrimaryLayout extends Component {
  componentDidMount() {
    const {
      location: { search, pathname },
    } = this.props;
    if (!(search.includes('__proto__'))) {
      const redirectUrlIndex = search.indexOf(RedirectURLKey);
      if (search && redirectUrlIndex > 0) {
        const redirectPath = search.substring(
          redirectUrlIndex + RedirectURLKey.length + 1,
        );
        this.props.history.replace(redirectPath);
      }
    }
    this.setActiveTab(pathname);

    document.addEventListener('user-inactive', this.handleInactivity);
  }

  componentWillUnmount() {
    document.removeEventListener('user-inactive', this.handleInactivity);
  }

  handleInactivity = () => {
    const { onLogout } = this.props;
    onLogout();
  };

  setActiveTab = (pathname) => {
    const { updateNavLink, toggleSubmenuAction } = this.props;
    let activeTab = 1;
    let subActiveTab;

    const isCustomerDetailsRoute = pathname.split('/')[1] === 'customer-details';

    if (isCustomerDetailsRoute) {
      activeTab = 6;
    } else {
      const routeKeys = Object.keys(PRIMARY_NAV);
      routeKeys.forEach((key) => {
        const routeObject = PRIMARY_NAV[key];
        if (routeObject.navigationPath) {
          if (routeObject.navigationPath === pathname) {
            activeTab = routeObject.id;
          }
        } else {
          routeObject.subNavConfig.forEach((subRouteObject) => {
            if (subRouteObject.navigationPath === pathname) {
              activeTab = routeObject.id;
              subActiveTab = subRouteObject.id;
              toggleSubmenuAction(routeObject.id);
            }
          });
        }
      });
    }
    updateNavLink(activeTab, subActiveTab, 'PrimaryLayout');
  }

  render() {
    const {
      history,
      shrinkNavBar,
      selectedTabId,
      selectedSubTabId,
      toggleSubmenuAction,
      toggleNavBarDisplayAction,
      onLogout,
      userName,
      userId,
      openSubMenuIds,
      updateNavLink,
      location,
      permissions,
      isConcurrentAudit,
      role,
    } = this.props;
    const pageContainerClass = classNames(
      'sidebar-l sidebar-o side-scroll header-navbar-fixed background-image',
      { 'sidebar-mini': shrinkNavBar },
      (isConcurrentAudit) ? 'concurrent-sidebar' : '',
    );

    if (location && location.pathname === '/swagger-ui' && permissions.includes(SWAGGER_PERMISSION)) {
      return <SwaggerWrapper />;
    }

    return (
      <>
        <ptm-wrapper />
        <div id="page-container" className={pageContainerClass}>
          {
            location && location.pathname === '/permission-denied'
              ? null
              : (
                <PrimaryNavigation
                  selectedTabId={selectedTabId}
                  selectedSubTabId={selectedSubTabId}
                  toggleSubmenu={toggleSubmenuAction}
                  openSubMenuIds={openSubMenuIds}
                  permissions={permissions}
                />
              )
          }
          {
            location && location.pathname === '/permission-denied'
              ? null
              : (
                <SearchHeader
                  userName={userName}
                  history={history}
                  toggleNavBarDisplayAction={toggleNavBarDisplayAction}
                  onLogout={onLogout}
                />
              )
          }
          <PrimaryRouter
            userName={userName}
            history={history}
            role={role}
            updateNavLink={updateNavLink}
            userId={userId}
            location={location}
            isConcurrentAudit={isConcurrentAudit}
            permissions={permissions}
          />
        </div>
      </>
    );
  }
}
const mapStateToProps = (state) => {
  const { auth, navigation } = state;
  const { loggedInUser } = auth;
  const {
    shrinkNavBar,
    selectedTabId,
    selectedSubTabId,
    openSubMenuIds,
  } = navigation;
  return {
    userName: loggedInUser.user_id,
    userId: (loggedInUser.agent_id && parseInt(loggedInUser.agent_id)),
    permissions: loggedInUser.permissions,
    shrinkNavBar,
    selectedTabId,
    selectedSubTabId,
    openSubMenuIds,
    role: loggedInUser.roles,
  };
};

const mapDispatchToProps = (dispatch) => ({
  updateNavLink: bindActionCreators(updateSelectedTab, dispatch),
  toggleNavBarDisplayAction: bindActionCreators(toggleNavBarDisplay, dispatch),
  onLogout: bindActionCreators(logoutActionV2, dispatch),
  toggleSubmenuAction: bindActionCreators(toggleSubmenu, dispatch),
});

PrimaryLayout.propTypes = {
  shrinkNavBar: PropTypes.bool.isRequired,
  history: PropTypes.object.isRequired,
  userName: PropTypes.string,
  openSubMenuIds: PropTypes.array.isRequired,
  selectedTabId: PropTypes.number.isRequired,
  selectedSubTabId: PropTypes.number,
  toggleSubmenuAction: PropTypes.func.isRequired,
  updateNavLink: PropTypes.func.isRequired,
  toggleNavBarDisplayAction: PropTypes.func.isRequired,
  onLogout: PropTypes.func.isRequired,
  location: PropTypes.object.isRequired,
  userId: PropTypes.number.isRequired,
  permissions: PropTypes.array.isRequired,
  isConcurrentAudit: PropTypes.bool.isRequired,
  role: PropTypes.array.isRequired,
};

PrimaryLayout.defaultProps = {
  userName: null,
  selectedSubTabId: null,
};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(PrimaryLayout);
