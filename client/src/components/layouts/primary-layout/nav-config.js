const primaryNavIds = {
  KYC_BANK_APPROVAL: 2,
  EQUITY_IR_APPROVAL: 3,
  NPS_IR_APPROVAL: 4,
  <PERSON><PERSON><PERSON>SEARCH: 5,
  ADVANCED_USER_SEARCH: 6,
  ESCALATION_CHANNELS: 7,
  IR_APPROVAL_NEW_C: 8,
  IR_APPROVAL_EXT_C: 9,
  EXISTING_INVESTOR: 10,
  ADMIN_ACTIONS: 11,
  AUDIT_LOG: 12,
  <PERSON><PERSON><PERSON>_DEFAULT_BANK_ACCOUNT: 13,
  AUDITOR_DASHBOARD: 17,
  RISK_PANEL: 14,
  EQUITY_ACCESS_PANEL: 15,
  NOMINEE_UPLOAD: 16,
  USER_ACTIVATION_DEACTIVATION: 17,
  EQUITY_EARLY_ACCESS_PANEL: 18,
  CVL_DASHBOARD: 19,
  CKYC_DETAILS: 20,
  SLEEKCARD_UPDATE: 21,
  REFER_USER: 22,
  ADDITIONAL_DOC_DASHBOARD: 23,
  SYSTEM_DOWNTIMES: 24,
  <PERSON>R<PERSON>_DORMANT: 25,
  ACCOUNT_CLOSURE: 27,
  <PERSON><PERSON><PERSON>RMASKING: 30,
  UPLOADFILE: 29,
  CALL_TAG: 26,
  REKYC_PROCESS: 31,
  AUDIT_DOCS: 32,
  PML_ON_BSE: 33,
  SUBSCRIPTION_SELF_PANEL: 34,
  AFFILIATE_CONTENT_VERIFICATION: 35,
  SEGMENT_DEACTIVATION: 36,
  BROKERAGE_SELF_PANEL: 37,
  CST_DATA: 38,
  LEAD_RESET: 39,
  POST_IR_EDD_AML: 40,
};

const existingInvestorSubNavIds = {
  BANK: 1,
  OTM: 2,
  NSE_MODIFY_FILE_UPLOAD: 3,
  NFT_SIGNATURE_VERIFICATION: 4,
  EQUITY_BANKS: 5,
  BANK_IFSC_UPDATE: 6,
  BSE_MODIFY_FILE_UPLOAD: 7,
};
const kycBankSubNavIds = {
  DOCUMENT_APPROVAL: 1,
  PARTIAL_DOCUMENT_APPROVAL: 2,
  IR_APPROVAL_NEW_C: 3,
  IR_APPROVAL_EXT_C: 4,
};
const equitySubNavIds = {
  EQUITY_IR_L1: 1,
  EQUITY_IR_L2: 2,
  EQUITY_IR_ADDRESS_L1: 3,
  EQUITY_IR_ADDRESS_L2: 4,
  NSE_FILE_UPLOAD: 5,
  NSE_ADDRESS_FAILURE: 6,
  EQUITY_ESIGN_VERIFICATION: 7,
  EQUITY_ACCOUNT_MODIFICATIONS: 9,
  BSE_FILE_UPLOAD: 10,
  PERMITTED_TO_TRADE: 11,
  REKYC_PROCESS: 12,
  HERCULES_RETRY: 13,
  EQUITY_ACCOUNT_CLOSURE: 14,
};
const npsSubNavIds = {
  NPS_IR_L1: 1,
  NPS_IR_L2: 2,
};
const adminActionsSubNavIds = {
  UPDATE_KYC: 1,
  UPDATE_EQUITY: 6,
  UPDATE_NPS: 7,
  RESET_USERS: 2,
  ADD_PIN_CODE: 3,
  EDIT_REJECT_REASON: 4,
  MARK_DORMANT: 5,
  ENCRYPT_INFO: 8,
  DECRYPT_INFO: 9,
  ACCOUNT_FREEZE_NUMBER: 10,
  CERSIA_KIN_REFERENCE_UPLOAD: 11,
};

const auditLogSubNavIds = {
  LIST: 1,
  SEARCH: 2,
};

const riskPanelSubNavIds = {
  AML: 1,
  IR_REVOKE: 2,
  FREEZE_UNFREEZE: 3,
};

const systemDowntimes = {
  SYSTEM_DOWNTIMES_CONF: 1,
};

const auditDocs = {
  AUDIT_DOCS_CONFIG: 1,
};

const PRIMARY_NAV = {
  AUDITOR_DASHBOARD: {
    id: primaryNavIds.AUDITOR_DASHBOARD,
    label: 'Concurrent Audit',
    iconClass: 'fa fa-tachometer',
    navigationPath: '/auditor-dashboard',
    permissions: ['CONCURRENT_AUDITOR'],
  },
  KYC_BANK_APPROVAL: {
    id: primaryNavIds.KYC_BANK_APPROVAL,
    label: 'KYC & Bank Approval',
    iconClass: 'fa fa-ticket',
    subNavConfig: [
      {
        id: kycBankSubNavIds.DOCUMENT_APPROVAL,
        label: 'Documents Approval',
        iconClass: 'fa fa-ticket',
        navigationPath: '/core-document',
        permissions: ['DOCUMENTS_APPROVAL'], // TODO: Fix nested permission issue
      },
      {
        id: kycBankSubNavIds.PARTIAL_DOCUMENT_APPROVAL,
        label: 'Doc Approval (Partial)',
        iconClass: 'fa fa-ticket',
        navigationPath: '/core-partial-document',
        permissions: ['DOC_APPROVAL_PARTIAL'],
      },
      {
        id: kycBankSubNavIds.IR_APPROVAL_NEW_C,
        label: 'IR Approval (NEW KYC)',
        iconClass: 'fa fa-check',
        navigationPath: '/core-ir-new-kyc',
        permissions: ['IR_APPROVAL_NEW_C'],
      },
      {
        id: kycBankSubNavIds.IR_APPROVAL_EXT_C,
        label: 'IR Approval (EXT. KYC)',
        iconClass: 'fa fa-ticket',
        navigationPath: '/core-ir-existing-kyc',
        permissions: ['IR_APPROVAL_EXT_C'],
      },
    ],
  },
  EQUITY_IR_APPROVAL: {
    id: primaryNavIds.EQUITY_IR_APPROVAL,
    label: 'Equity IR Approval',
    iconClass: 'fa fa-ticket',
    subNavConfig: [
      {
        id: equitySubNavIds.EQUITY_IR_L1,
        label: 'IR L1',
        iconClass: 'fa fa-check',
        navigationPath: '/equity-l1',
        permissions: ['EQ_IR_L1'],
      },
      {
        id: equitySubNavIds.EQUITY_IR_L2,
        label: 'IR L2',
        iconClass: 'fa fa-ticket',
        navigationPath: '/equity-l2',
        permissions: ['EQ_IR_L2'],
      },
      {
        id: equitySubNavIds.EQUITY_IR_ADDRESS_L1,
        label: 'Address Approval L1',
        iconClass: 'fa fa-check',
        navigationPath: '/equity-address-l1',
        permissions: ['EQ_ADDRESS_L1'],
      },
      {
        id: equitySubNavIds.EQUITY_IR_ADDRESS_L2,
        label: 'Address Approval L2',
        iconClass: 'fa fa-ticket',
        navigationPath: '/equity-address-l2',
        permissions: ['EQ_ADDRESS_L2'],
      },
      {
        id: equitySubNavIds.EQUITY_ESIGN_VERIFICATION,
        label: 'ESign Verification',
        iconClass: 'fa fa-ticket',
        navigationPath: '/equity-esign-verification',
        permissions: ['DOCUMENTS_APPROVAL'],
      },
      {
        id: equitySubNavIds.NSE_FILE_UPLOAD,
        label: 'NSE File Upload',
        iconClass: 'fa fa-ticket',
        navigationPath: '/nse-file-upload',
        permissions: ['EQ_NSE_CREATION'],
      },
      {
        id: equitySubNavIds.BSE_FILE_UPLOAD,
        label: 'BSE File Upload',
        iconClass: 'fa fa-ticket',
        navigationPath: '/bse-file-upload',
        permissions: ['EQ_NSE_CREATION'],
      },
      {
        id: equitySubNavIds.PERMITTED_TO_TRADE,
        label: 'Permitted to Trade File',
        iconClass: 'fa fa-ticket',
        navigationPath: '/permitted-to-trade-file-upload',
        permissions: ['EQ_NSE_CREATION'],
      },
      {
        id: equitySubNavIds.NSE_ADDRESS_FAILURE,
        label: 'Hercules Failures',
        iconClass: 'fa fa-ticket',
        navigationPath: '/hercules-failure',
        permissions: ['EQ_ADDRESS_FAIL'],
      },
      {
        id: equitySubNavIds.EQUITY_ACCOUNT_MODIFICATIONS,
        label: 'Account Modification',
        iconClass: 'fa fa-user-edit',
        navigationPath: '/equity-account-modifications',
        permissions: ['ACCOUNT_MODIFICATION_VERIFICATION'],
      },
      {
        id: equitySubNavIds.REKYC_PROCESS,
        label: 'Rekyc Process',
        iconClass: 'fa fa-user-edit',
        navigationPath: '/rekyc-process',
        permissions: ['ACCOUNT_MODIFICATION_VERIFICATION'],
      },
      {
        id: equitySubNavIds.HERCULES_RETRY,
        label: 'Hercules Retry',
        iconClass: 'fa fa-ticket',
        navigationPath: '/hercules-retry',
        permissions: ['EQ_ADDRESS_FAIL'],
      },
      {
        id: equitySubNavIds.EQUITY_ACCOUNT_CLOSURE,
        label: 'Account Closure',
        iconClass: 'fa fa-user-edit',
        navigationPath: '/equity-account-closure',
        permissions: ['ACCOUNT_CLOSURE'],
      },
    ],
  },
  NPS_IR_APPROVAL: {
    id: primaryNavIds.NPS_IR_APPROVAL,
    label: 'NPS IR Approval',
    iconClass: 'fa fa-ticket',
    subNavConfig: [
      {
        id: npsSubNavIds.NPS_IR_L1,
        label: 'IR L1',
        iconClass: 'fa fa-check',
        navigationPath: '/nps-l1',
        permissions: ['NPS_IR_l1'],
      },
      {
        id: npsSubNavIds.NPS_IR_L2,
        label: 'IR L2',
        iconClass: 'fa fa-ticket',
        navigationPath: '/nps-l2',
        permissions: ['NPS_IR_l2'],
      },
    ],
  },
  ADVANCE_USER_SEARCH: {
    id: primaryNavIds.ADVANCED_USER_SEARCH,
    label: 'Advanced User Search',
    iconClass: 'fa fa-search',
    navigationPath: '/advanced-user-search',
    permissions: ['ADVANCE_USER_SEARCH'],
  },
  ESCALATION_CHANNELS: {
    id: primaryNavIds.ESCALATION_CHANNELS,
    label: 'Escalation Channels',
    iconClass: 'fa fa-address-book',
    navigationPath: '/escalation-channels',
    permissions: ['ESCALATION_CHANNELS'],
  },
  EXISTING_INVESTOR: {
    id: primaryNavIds.EXISTING_INVESTOR,
    label: 'Existing Investor',
    iconClass: 'fa fa-repeat',
    subNavConfig: [
      {
        id: existingInvestorSubNavIds.BANK,
        label: 'BANK',
        navigationPath: '/existing-investor-bank',
        permissions: ['EXISTING_INVESTOR'],
      },
      {
        id: existingInvestorSubNavIds.EQUITY_BANKS,
        label: 'EQUITY BANKS',
        navigationPath: '/existing-investor-equity-banks',
        permissions: ['EXISTING_INVESTOR'],
      },
      {
        id: existingInvestorSubNavIds.BANK_IFSC_UPDATE,
        label: 'BANK IFSC UPDATE',
        navigationPath: '/existing-investor-bank-ifsc-update',
        permissions: ['EXISTING_INVESTOR'],
      },
      {
        id: existingInvestorSubNavIds.OTM,
        label: 'OTM',
        navigationPath: '/existing-investor-otm',
        permissions: ['EXISTING_INVESTOR'],
      },
      {
        id: existingInvestorSubNavIds.NSE_MODIFY_FILE_UPLOAD,
        label: 'NSE MODIFY FILE UPLOAD',
        navigationPath: '/nse-modify-file-upload',
        permissions: ['EXISTING_INVESTOR'],
      },
      {
        id: existingInvestorSubNavIds.BSE_MODIFY_FILE_UPLOAD,
        label: 'BSE MODIFY FILE UPLOAD',
        navigationPath: '/bse-modify-file-upload',
        permissions: ['EXISTING_INVESTOR'],
      },
      {
        id: existingInvestorSubNavIds.NFT_SIGNATURE_VERIFICATION,
        label: 'Signature Verification',
        iconClass: 'fa fa-ticket',
        navigationPath: '/nft-signature-verification',
        permissions: ['NFT_SIGNATURE'],
      },
    ],
  },
  ADMIN_ACTIONS: {
    id: primaryNavIds.ADMIN_ACTIONS,
    label: 'Admin Actions',
    iconClass: 'si si-user-following',
    subNavConfig: [
      {
        id: adminActionsSubNavIds.UPDATE_KYC,
        label: 'Update KYC & Bank',
        navigationPath: '/update-kyc',
        permissions: ['ADMIN_ACTIONS'],
      },
      {
        id: adminActionsSubNavIds.UPDATE_EQUITY,
        label: 'Update Equity',
        navigationPath: '/update-equity',
        permissions: ['ADMIN_ACTIONS'],
      },
      {
        id: adminActionsSubNavIds.UPDATE_NPS,
        label: 'Update NPS',
        navigationPath: '/update-nps',
        permissions: ['ADMIN_ACTIONS'],
      },
      {
        id: adminActionsSubNavIds.RESET_USERS,
        label: 'Reset User Info',
        navigationPath: '/reset-user-info',
        permissions: ['ADMIN_ACTIONS'],
      },
      {
        id: adminActionsSubNavIds.ADD_PIN_CODE,
        label: 'Add Pin Code',
        navigationPath: '/add-pin-code',
        permissions: ['ADMIN_ACTIONS'],
      },
      {
        id: adminActionsSubNavIds.EDIT_REJECT_REASON,
        label: 'Edit Reject Reason',
        navigationPath: '/edit-reject-reason',
        permissions: ['ADMIN_ACTIONS'],
      },
      {
        id: adminActionsSubNavIds.MARK_DORMANT,
        label: 'Bulk Actions',
        navigationPath: '/mark-dormant',
        permissions: ['ADMIN_ACTIONS'],
      },
      {
        id: adminActionsSubNavIds.ENCRYPT_INFO,
        label: 'Encrypt Information',
        navigationPath: '/encrypt-information',
        permissions: ['PII_ENCRYPTION'],
      },
      {
        id: adminActionsSubNavIds.DECRYPT_INFO,
        label: 'Decrypt Information',
        navigationPath: '/decrypt-information',
        permissions: ['PII_DECRYPTION'],
      },
      {
        id: adminActionsSubNavIds.ACCOUNT_FREEZE_NUMBER,
        label: 'Account Freeze Number',
        navigationPath: '/account-freeze-number',
        permissions: ['ADMIN_ACTIONS'],
      },
      {
        id: adminActionsSubNavIds.CERSIA_KIN_REFERENCE_UPLOAD,
        label: 'Cersia KIN Reference Upload',
        navigationPath: '/cersia-kin-reference-upload',
        permissions: ['ADMIN_ACTIONS'],
      },
    ],
  },
  NOMINEE_UPLOAD: {
    id: primaryNavIds.NOMINEE_UPLOAD,
    label: 'Nominee Upload',
    iconClass: 'si si-user-following',
    navigationPath: '/nominee-upload',
    permissions: ['EQ_NOMINEE'],
  },
  LEAD_RESET: {
    id: primaryNavIds.LEAD_RESET,
    label: 'Lead Reset',
    iconClass: 'si si-user-following',
    navigationPath: '/lead-reset',
    permissions: ['ADMIN_ACTIONS'],
  },
  POST_IR_EDD_AML: {
    id: primaryNavIds.POST_IR_EDD_AML,
    label: 'Post IR EDD in AML',
    iconClass: 'si si-user-following',
    navigationPath: '/post-ir-edd-aml',
    permissions: ['ADMIN_ACTIONS'],
  },
  CHANGE_DEFAULT_BANK_ACCOUNT: {
    id: primaryNavIds.CHANGE_DEFAULT_BANK_ACCOUNT,
    label: 'Change Default Bank',
    iconClass: 'fa fa-ticket',
    navigationPath: '/change-default-bank',
    permissions: ['CNG_DEF_BANK'],
  },
  AUDIT_LOG: {
    id: primaryNavIds.AUDIT_LOG,
    label: 'Audit Log',
    iconClass: 'fa fa-history',
    subNavConfig: [
      {
        id: auditLogSubNavIds.LIST,
        label: 'List',
        navigationPath: '/audit-log-list',
        permissions: ['AUDIT_LOG'],
      },
      {
        id: auditLogSubNavIds.SEARCH,
        label: 'Search',
        navigationPath: '/audit-log-search',
        permissions: ['AUDIT_LOG'],
      },
    ],
  },

  RISK_PANEL: {
    id: primaryNavIds.RISK_PANEL,
    label: 'Risk Panel',
    iconClass: 'fa fa-history',
    subNavConfig: [
      {
        id: riskPanelSubNavIds.AML,
        label: 'AML',
        navigationPath: '/risk-panel-aml',
        permissions: ['AML'],
      },
      {
        id: riskPanelSubNavIds.IR_REVOKE,
        label: 'IR Revoke/ Revert Revoke',
        navigationPath: '/risk-panel-ir-revoke-activate',
        permissions: ['IR_REVOKE'],
      },
      {
        id: riskPanelSubNavIds.FREEZE_UNFREEZE,
        label: 'Volunteer Freeze/Unfreeze',
        navigationPath: '/risk-panel-freeze-unfreeze',
        permissions: ['IR_REVOKE'],
      },
    ],
  },

  EQUITY_ACCESS_PANEL: {
    id: primaryNavIds.EQUITY_ACCESS_PANEL,
    label: 'Equity Access Panel',
    iconClass: 'fa fa-history',
    permissions: ['EQ_ACCESS'],
    navigationPath: '/equity-access-panel',
  },
  EQUITY_EARLY_ACCESS_PANEL: {
    id: primaryNavIds.EQUITY_EARLY_ACCESS_PANEL,
    label: 'FnO Beta Access',
    iconClass: 'fa fa-history',
    permissions: ['EQ_EARLY_ACCESS_PANEL'],
    navigationPath: '/equity-early-access-panel',
  },
  USER_ACTIVATION_DEACTIVATION: {
    id: primaryNavIds.USER_ACTIVATION_DEACTIVATION,
    label: 'Activate/Deactivate',
    iconClass: 'fa fa-history',
    permissions: ['ACCOUNT_ACT_DEACT'],
    navigationPath: '/activate-deactivate',
  },
  ACCOUNT_CLOSURE: {
    id: primaryNavIds.ACCOUNT_CLOSURE,
    label: 'Account Closure',
    iconClass: 'fa fa-user-times',
    navigationPath: '/account-closure',
    permissions: ['ACCOUNT_CLOSURE'],
  },
  SEGMENT_DEACTIVATION: {
    id: primaryNavIds.SEGMENT_DEACTIVATION,
    label: 'Segment Deactivation Tab',
    iconClass: 'fa fa-user-times',
    navigationPath: '/segment-deactivation',
    permissions: ['CS_EQ_DEACTIVATION_SEGMENT', 'OPS_EQ_DEACTIVATION_SEGMENT'],
  },
  CVL_DASHBOARD: {
    id: primaryNavIds.CVL_DASHBOARD,
    label: 'CVL Dashboard',
    iconClass: 'fa fa-history',
    permissions: ['GANGA_CVL_DASHBOARD'],
    navigationPath: '/cvl-dashboard',
  },
  ADDITIONAL_DOC_DASHBOARD: {
    id: primaryNavIds.ADDITIONAL_DOC_DASHBOARD,
    label: 'Additional Doc',
    iconClass: 'fa fa-tachometer',
    navigationPath: '/additional-doc-dashboard',
    permissions: ['ADDITIONAL_DOC_DASHBOARD'],
  },
  CKYC_DETAILS: {
    id: primaryNavIds.CKYC_DETAILS,
    label: 'CKYC Details',
    iconClass: 'si si-user-following',
    permissions: ['CKYC_UPDATION'],
    navigationPath: '/ckyc-details',
  },
  SLEEKCARD_UPDATE: {
    id: primaryNavIds.SLEEKCARD_UPDATE,
    label: 'Sleekcard Update',
    iconClass: 'fa fa-history',
    permissions: ['SLEEKCARD_UPDATE'],
    navigationPath: '/sleekcard-update',
  },
  SYSTEM_DOWNTIMES: {
    id: primaryNavIds.SYSTEM_DOWNTIMES,
    label: 'System Downtimes',
    iconClass: 'fa fa-server',
    subNavConfig: [
      {
        id: systemDowntimes.SYSTEM_DOWNTIMES_CONF,
        label: 'Hercules Downtime Configuration',
        navigationPath: '/system-downtime',
        permissions: ['EQ_NSE_CREATION'],
      },
    ],
  },
  // CALL_TAG: {
  //   id: primaryNavIds.CALL_TAG,
  //   label: 'Call Tag',
  //   iconClass: 'si si-user-following',
  //   navigationPath: '/call-tag',
  //   permissions: ['BASIC_DETAILS'],
  // },
  UPLOADFILE: {
    id: primaryNavIds.UPLOADFILE,
    label: 'User Campaign',
    iconClass: 'si si-user-following',
    navigationPath: '/UploadFile',
    permissions: ['BASIC_DETAILS'],
  },
  AADHARMASKING: {
    id: primaryNavIds.AADHARMASKING,
    label: 'Aadhar Masking',
    iconClass: 'fa fa-history',
    navigationPath: '/aadhar-masking',
    permissions: ['EQ_IR_L1'],
  },
  AUDIT_DOCS: {
    id: primaryNavIds.AUDIT_DOCS,
    label: 'Audit Docs',
    iconClass: 'fa fa-tachometer',
    subNavConfig: [
      {
        id: auditDocs.AUDIT_DOCS_CONFIG,
        label: 'For Auditors',
        navigationPath: '/audit-docs',
        permissions: ['ADDITIONAL_DOC_DASHBOARD'],
      },
    ],
  },
  PML_ON_BSE: {
    id: primaryNavIds.PML_ON_BSE,
    label: 'PML on Bse',
    iconClass: 'fa fa-history',
    navigationPath: '/pml-on-bse',
    permissions: ['BSE_ENABLE_FLAG'],
  },
  SUBSCRIPTION_SELF_PANEL: {
    id: primaryNavIds.SUBSCRIPTION_SELF_PANEL,
    label: 'Subscription Self Panel',
    iconClass: 'fa fa-tachometer',
    navigationPath: '/subscription-self',
    permissions: ['SUBSCRIPTION_EXTENSION'],
  },
  BROKERAGE_SELF_PANEL: {
    id: primaryNavIds.BROKERAGE_SELF_PANEL,
    label: 'Brokerage Self Panel',
    iconClass: 'fa fa-tachometer',
    navigationPath: '/brokerage-self',
    permissions: ['CHANGE_BROKERAGE'],
  },
  AFFILIATE_CONTENT_VERIFICATION: {
    id: primaryNavIds.AFFILIATE_CONTENT_VERIFICATION,
    label: 'Affiliate Content Verification',
    iconClass: 'fa fa-server',
    navigationPath: '/affiliate-content-verification',
    permissions: ['Affiliate_content_verification'],
  },
  CST_DATA: {
    id: primaryNavIds.CST_DATA,
    label: 'CST Data',
    iconClass: 'fa fa-server',
    navigationPath: '/view-cst-data',
    permissions: ['ADMIN_ACTIONS'],
  },
};

const SWAGGER_PERMISSION = 'GANGA_SWAGGER';

export {
  primaryNavIds,
  existingInvestorSubNavIds,
  adminActionsSubNavIds,
  auditLogSubNavIds, // TODO: check if needed
  kycBankSubNavIds,
  equitySubNavIds,
  npsSubNavIds,
  riskPanelSubNavIds,
  PRIMARY_NAV,
  SWAGGER_PERMISSION,
};
