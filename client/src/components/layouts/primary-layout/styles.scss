.input-header-search{
    .form-control{
        height: 30px;
        background: transparent;
        color: #ffffff;
    }
    .btn-primary-dark{
        max-height: 30px;
        background: #ffffff;
        color: #646464;
        padding: 5px 10px;
        border: 1px solid #ffffff;
    }
    .fa{
        font-size: 12px;
    }
}
.primary-nav {
    &.nav-main {
        width: initial;
    }
    .sidebar-mini-hide {
        margin-left: 12px;
    }
    .nav-tab-style {
        padding: 0;
    }
    li > button:hover {
        background-color: rgba(0, 0, 0, 0.2);
        color: #fff;
    }
    & button {
        width: 100%;
        text-align: left;
        padding: 8px 16px;
        color: rgba(255, 255, 255, 0.5);
        position: relative;
    }
    & button::before {
        position: absolute;
        top: 50%;
        -webkit-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        transform: translateY(-50%);
        right: 12px;
        display: inline-block;
        font-family: 'FontAwesome';
        color: rgba(255, 255, 255, 0.25);
        content: "\f104";
    }
    & .open button::before {
        content: "\f107";
    }
    .sub-nav {
        margin: 0;
        padding: 0 0 0 40px;
        height: 0;
        list-style: none;
        background-color: rgba(0, 0, 0, 0.15);
        overflow: hidden;
        & a {
            padding: 6px 6px 6px 0;
            font-size: 10px;
            color: rgba(255, 255, 255, 0.4);
        }
        & a:hover, button:hover {
            background-color: initial;
        }
        & .active {
            color:  #fff;
        }
        & .sidebar-mini-hide {
            margin-left: 0;
        }
    }
    .open .sub-nav {
        height: auto;
    }
}