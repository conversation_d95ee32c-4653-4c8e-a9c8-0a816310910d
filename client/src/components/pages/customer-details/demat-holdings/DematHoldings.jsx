import React from 'react';
import PropTypes from 'prop-types';
import { Table, Pagination } from 'antd';
import { PtmToast } from '@paytm-money/frontend-common-v2';

const DematHoldings = ({
  isFetching,
  error,
  dematHoldingsData,
  totalCount,
  pageNumber,
  pageSize,
  onChangePageNumber,
  tableHeaderConfig,
}) => {
  if (error) {
    return (
      <PtmToast
        message={error}
        isVisible={Boolean(error)}
        type="error"
      />
    );
  }

  const handlePageChange = (page) => {
    onChangePageNumber(page);
  };

  return (
    <div className="block">
      <div className="block-header">
        <h3 className="block-title">Demat Holdings</h3>
      </div>
      <div className="block-content">
        <Table
          dataSource={dematHoldingsData}
          columns={tableHeaderConfig}
          loading={isFetching}
          pagination={false}
          rowKey="isin"
          size="small"
          scroll={{ x: true }}
        />
        {totalCount > pageSize && (
          <div className="text-center mt-3">
            <Pagination
              current={pageNumber}
              total={totalCount}
              pageSize={pageSize}
              onChange={handlePageChange}
              showSizeChanger={false}
              showQuickJumper
              showTotal={(total, range) =>
                `${range[0]}-${range[1]} of ${total} items`
              }
            />
          </div>
        )}
      </div>
    </div>
  );
};

DematHoldings.propTypes = {
  isFetching: PropTypes.bool.isRequired,
  error: PropTypes.string,
  dematHoldingsData: PropTypes.array,
  totalCount: PropTypes.number.isRequired,
  pageNumber: PropTypes.number.isRequired,
  pageSize: PropTypes.number.isRequired,
  onChangePageNumber: PropTypes.func.isRequired,
  tableHeaderConfig: PropTypes.array.isRequired,
};

DematHoldings.defaultProps = {
  error: null,
  dematHoldingsData: [],
};

export default DematHoldings;
