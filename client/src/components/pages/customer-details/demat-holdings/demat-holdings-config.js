import React from 'react';

/**
 * Table header configuration for Demat Holdings
 */
export const getTableHeaderConfig = () => [
  {
    title: 'ISIN',
    dataIndex: 'isin',
    key: 'isin',
    width: '15%',
    render: (text) => <span className="text-primary">{text}</span>,
  },
  {
    title: 'Security Name',
    dataIndex: 'name',
    key: 'name',
    width: '25%',
    render: (text) => <span className="font-weight-semibold">{text}</span>,
  },
  {
    title: 'BOID',
    dataIndex: 'boid',
    key: 'boid',
    width: '15%',
  },
  {
    title: 'Free Qty',
    dataIndex: 'free_qty',
    key: 'free_qty',
    width: '10%',
    render: (value) => <span className="text-success">{value}</span>,
  },
  {
    title: 'Pledged Qty',
    dataIndex: 'pledged_qty',
    key: 'pledged_qty',
    width: '10%',
    render: (value) => <span className="text-warning">{value}</span>,
  },
  {
    title: 'Locked Qty',
    dataIndex: 'locked_qty',
    key: 'locked_qty',
    width: '10%',
    render: (value) => <span className="text-danger">{value}</span>,
  },
  {
    title: 'As On Date',
    dataIndex: 'as_on_date',
    key: 'as_on_date',
    width: '15%',
  },
];

/**
 * Default page size for pagination
 */
export const DEFAULT_PAGE_SIZE = 25;
