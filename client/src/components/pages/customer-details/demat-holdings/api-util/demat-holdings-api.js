import APIService from '../../../../../services/ApiService';
import { ENDPOINTS } from '../../../../../constants/api-constants';

/**
 * Fetch demat holdings data for a customer
 * @param {number} customerId - Customer ID
 * @param {number} pageNo - Page number (0-based)
 * @param {number} pageSize - Number of items per page
 * @returns {Promise} API response promise
 */
export const fetchDematHoldingsData = (customerId, pageNo = 0, pageSize = 10) => {
  const params = {
    page_no: pageNo,
    page_size: pageSize,
  };

  // For now, using a placeholder endpoint - this should be updated with the actual endpoint
  const reqObj = {
    url: `/api/customers/${customerId}/demat-holdings`,
    method: 'GET',
  };

  return APIService.makeRequest(reqObj, params, customerId);
};
