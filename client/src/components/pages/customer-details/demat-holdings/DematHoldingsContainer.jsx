import React, { Component } from 'react';
import PropTypes from 'prop-types';
import autobind from 'autobind-decorator';
import WithLoadingHOC from '../CustomerDetailsPageHOC';
import DematHoldings from './DematHoldings';
import { fetchDematHoldingsData } from './api-util/demat-holdings-api';
import { getTableHeaderConfig, DEFAULT_PAGE_SIZE } from './demat-holdings-config';

const PageHOC = WithLoadingHOC(DematHoldings);

class DematHoldingsContainer extends Component {
  constructor() {
    super();
    this.state = {
      isFetching: false,
      error: null,
      dematHoldingsData: [],
      pageNumber: 1,
      pageSize: DEFAULT_PAGE_SIZE,
      totalCount: 0,
    };
    this.tableHeaderConfig = getTableHeaderConfig();
  }

  componentDidMount() {
    this.fetchDematHoldingsData();
  }

  @autobind
  onChangePageNumber(pageNumber) {
    if ((pageNumber === this.state.pageNumber) || (pageNumber === 0)) {
      return;
    }
    this.setState({
      pageNumber,
    });
    this.fetchDematHoldingsData(pageNumber, this.state.pageSize);
  }

  @autobind
  fetchDematHoldingsData(pageNumber = 1, pageSize = DEFAULT_PAGE_SIZE) {
    const { customerId } = this.props;
    
    this.setState({
      isFetching: true,
      error: null,
    });

    // Convert to 0-based page number for API
    const pageNo = pageNumber - 1;

    fetchDematHoldingsData(customerId, pageNo, pageSize)
      .then((response) => {
        const { data } = response;
        const { results = [], page_context = {} } = data.data || {};
        
        this.setState({
          dematHoldingsData: results,
          totalCount: page_context.total_count || results.length,
          isFetching: false,
          error: null,
        });
      })
      .catch((error) => {
        console.error('Error fetching demat holdings:', error);
        this.setState({
          isFetching: false,
          error: error.message || 'Failed to fetch demat holdings data',
          dematHoldingsData: [],
          totalCount: 0,
        });
      });
  }

  render() {
    const {
      isFetching,
      error,
      dematHoldingsData,
      totalCount,
      pageNumber,
      pageSize,
    } = this.state;

    return (
      <PageHOC
        loading={isFetching}
        error={error}
        dematHoldingsData={dematHoldingsData}
        totalCount={totalCount}
        pageNumber={pageNumber}
        pageSize={pageSize}
        onChangePageNumber={this.onChangePageNumber}
        tableHeaderConfig={this.tableHeaderConfig}
      />
    );
  }
}

DematHoldingsContainer.propTypes = {
  customerId: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
};

export default DematHoldingsContainer;
