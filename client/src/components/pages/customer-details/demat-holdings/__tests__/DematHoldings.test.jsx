import React from 'react';
import { shallow } from 'enzyme';
import DematHoldings from '../DematHoldings';

describe('DematHoldings Component', () => {
  const defaultProps = {
    isFetching: false,
    error: null,
    dematHoldingsData: [
      {
        cust_id: '231478903',
        boid: '1208880058178114',
        isin: 'INE669E01016',
        free_qty: 2.0,
        pledged_qty: 0.0,
        locked_qty: 0.0,
        earmark_qty: 0.0,
        safe_keep_qty: 0.0,
        boid_frz: 'DF',
        isin_frz: 'DF',
        bo_isin_frz: 'DF',
        name: 'Vodafone Idea 1',
        as_on_date: '12-08-2025',
      },
    ],
    totalCount: 1,
    pageNumber: 1,
    pageSize: 25,
    onChangePageNumber: jest.fn(),
    tableHeaderConfig: [],
  };

  it('should render without crashing', () => {
    const wrapper = shallow(<DematHoldings {...defaultProps} />);
    expect(wrapper).toBeDefined();
  });

  it('should display error message when error prop is provided', () => {
    const props = {
      ...defaultProps,
      error: 'Test error message',
    };
    const wrapper = shallow(<DematHoldings {...props} />);
    expect(wrapper.find('PtmToast')).toHaveLength(1);
  });

  it('should render table with data', () => {
    const wrapper = shallow(<DematHoldings {...defaultProps} />);
    expect(wrapper.find('Table')).toHaveLength(1);
    expect(wrapper.find('.block-title').text()).toBe('Demat Holdings');
  });
});
