import React from 'react';
import PropTypes from 'prop-types';
import Select from 'react-select';

import { PtmButton, PtmInput } from '@paytm-money/frontend-common-v2';

const AddPinCode = (props) => {
  const {
    stateValues,
    cityValues,
    savePin,
    pinCode,
    onChange,
    onPinChange,
    disableSaveButton,
    getLocationValues,
  } = props;
  return (
    <div id="main-container" className="full-height">
      <div className="push-100-r push-100-l push-100-t">
        <h4 className="push-20"> Add Pin Code </h4>
        <div className="bg-white pin-form-content">
          <div>
            <div className="row push-15-t">
              <div className="col-md-9">
                <div className="form-group">
                  <PtmInput
                    id="pincode"
                    showLabel
                    roundedCorners
                    name="pin-code"
                    label="Pin Code"
                    placeholder="Please enter Pin Code"
                    onChange={onPinChange}
                    value={pinCode}
                  />
                </div>
              </div>
            </div>
            <div className="row push-15-t">
              <div className="col-md-9" role="presentation" onClick={getLocationValues}>
                <div className="form-group">
                  <div className="form-material">
                    <Select
                      onChange={(params) => {
                        onChange(
                          'stateCode',
                          params,
                        );
                      }}
                      options={stateValues}
                      name="state-code"
                    />
                    <label htmlFor="material-text">State</label>
                  </div>
                </div>
              </div>
            </div>
            <div className="row push-15-t">
              <div className="col-md-9" role="presentation" onClick={getLocationValues}>
                <div className="form-group">
                  <div className="form-material">
                    <Select
                      onChange={(params) => {
                        onChange(
                          'cityCode',
                          params,
                        );
                      }}
                      options={cityValues}
                      name="cityCode"
                    />
                    <label htmlFor="material-text">City</label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="push-50 push-15-t push-15-l">
            <PtmButton
              appearance="primary"
              handlePress={savePin}
              disabled={disableSaveButton}
            >
              Save
            </PtmButton>
          </div>
        </div>
      </div>
    </div>
  );
};

AddPinCode.propTypes = {
  stateValues: PropTypes.array.isRequired,
  cityValues: PropTypes.array.isRequired,
  pinCode: PropTypes.string.isRequired,
  savePin: PropTypes.func.isRequired,
  onChange: PropTypes.func.isRequired,
  disableSaveButton: PropTypes.bool.isRequired,
  getLocationValues: PropTypes.func.isRequired,
  onPinChange: PropTypes.func.isRequired,
};

export default AddPinCode;
