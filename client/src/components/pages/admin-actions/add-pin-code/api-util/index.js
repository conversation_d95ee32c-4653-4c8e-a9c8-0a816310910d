import ApiService from '../../../../../services/ApiService';
import { ENDPOINTS } from '../../../../../constants/api-constants';

const fetchStateValues = () => ApiService.makeRequest(ENDPOINTS.adminActions.getStateData);
const fetchCityValues = () => ApiService.makeRequest(ENDPOINTS.adminActions.getCityData);

const createPinCode = (params, pinCode) => {
  const reqURL = ENDPOINTS.adminActions.addPinCode;
  return ApiService.makeRequest(reqURL, params, pinCode);
};

export {
  fetchStateValues,
  fetchCityValues,
  createPinCode,
};
