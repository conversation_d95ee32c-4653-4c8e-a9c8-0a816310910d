import React from 'react';
import { PtmToast } from '@paytm-money/frontend-common-v2';
import autobind from 'autobind-decorator';
import AddPinCode from './AddPinCode';
import { fetchCityValues, fetchStateValues, createPinCode } from './api/add-pincode-api';

const INPUT_VALUES = {
  PIN: 'PIN',
  STATE: 'STATE',
  CITY: 'CITY',
};

class AddPinCodeContainer extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      stateValues: [],
      cityValues: [],
      pinCode: '',
      stateCode: null,
      cityCode: null,
      isSaveDisabled: true,
      successMessage: '',
      successToastVisible: false,
      errorMessage: '',
      errorToastVisible: false,
      callGetLocation: true,
    };
  }

  @autobind
  onPinChange(event) {
    let { pinCode } = this.state;
    const { value } = event.target;
    const pinParsed = parseInt(value, 10);
    if (!Number.isNaN(pinParsed)) {
      pinCode = pinParsed;
    } else if (value === '') {
      pinCode = value;
    }
    this.setState({
      pinCode,
    }, () => {
      this.updateSaveButton();
    });
  }

  @autobind
  onChange(name, params) {
    const { value } = params;
    this.setState({
      [name]: value,
    }, () => {
      this.updateSaveButton();
    });
  }

  @autobind
  getLocationValues() {
    const { callGetLocation } = this.state;
    if (callGetLocation) {
      this.getStateValues();
      this.getCityValues();
    }
  }

  getStateValues() {
    fetchStateValues()
      .then((response) => {
        const stateValues = response.map(ele => ({
          label: ele.name,
          value: ele.id,
        }));
        this.setState({
          stateValues,
          callGetLocation: false,
        });
      })
      .catch((error) => {
        let errorMessage = 'Something went wrong!';
        if (error && error.error) {
          errorMessage = error.error;
        }
        this.setState({
          errorMessage,
          errorToastVisible: true,
        });
      });
  }

  getCityValues() {
    fetchCityValues()
      .then((response) => {
        const cityValues = response.map(ele => ({
          label: ele.name,
          value: ele.id,
        }));
        this.setState({
          cityValues,
          callGetLocation: false,
        });
      })
      .catch((error) => {
        let errorMessage = 'Something went wrong!';
        if (error && error.error) {
          errorMessage = error.error;
        }
        this.setState({
          errorMessage,
          errorToastVisible: true,
        });
      });
  }

  @autobind
  savePin() {
    const {
      pinCode,
      stateCode,
      cityCode,
    } = this.state;
    const params = {
      pinCode,
      cityId: cityCode,
      stateId: stateCode,
    };
    let errorMessage;
    if (pinCode.toString().length === 6) {
      this.setState({
        isSaveDisabled: true,
      });
      createPinCode(params, pinCode)
        .then((response) => {
          this.setState({
            successToastVisible: true,
            successMessage: response.data,
          });
        })
        .catch((error) => {
          errorMessage = 'Something went wrong!';
          if (error && error.error) {
            errorMessage = error.error;
          }
          this.setState({
            errorMessage,
            errorToastVisible: true,
          });
        });
    } else {
      errorMessage = 'Not Valid Pin Code';
      this.setState({
        errorMessage,
        errorToastVisible: true,
      });
    }
  }

  updateSaveButton() {
    const { pinCode, stateCode, cityCode } = this.state;
    let isSaveDisabled = true;
    if (pinCode && stateCode && cityCode) {
      isSaveDisabled = false;
    }
    this.setState({ isSaveDisabled });
  }

  @autobind
  handleSuccessToastAutohide(visibility) {
    this.setState({ successToastVisible: visibility });
  }

  @autobind
  handleErrorToastAutohide(visibility) {
    this.setState({ errorToastVisible: visibility });
  }

  render() {
    const {
      stateValues,
      cityValues,
      pinCode,
      stateCode,
      cityCode,
      isSaveDisabled,
      successMessage,
      successToastVisible,
      errorMessage,
      errorToastVisible,
    } = this.state;
    return (
      <div>
        <AddPinCode
          stateValues={stateValues}
          cityValues={cityValues}
          pinCode={pinCode}
          stateCode={stateCode}
          cityCode={cityCode}
          savePin={this.savePin}
          onChange={this.onChange}
          onPinChange={this.onPinChange}
          INPUT_VALUES={INPUT_VALUES}
          disableSaveButton={isSaveDisabled}
          getLocationValues={this.getLocationValues}
        />
        <PtmToast
          message={successMessage}
          isVisible={successToastVisible}
          changeState={this.handleSuccessToastAutohide}
          containerClass="success"
        />
        <PtmToast
          message={errorMessage}
          isVisible={errorToastVisible}
          changeState={this.handleErrorToastAutohide}
          containerClass="error"
        />
      </div>
    );
  }
}

export default AddPinCodeContainer;
