import React from 'react';
import PropTypes from 'prop-types';
import { PtmButton, PtmCheckbox, PtmToast } from '@paytm-money/frontend-common-v2';
import {
  fetchUserBankDetails,
  uploadBankCancelledCheque,
} from './api/api-utils';
import Loader from '../../../common/Loader';
import {
  BANK_FILTERS, TABLE_HEADERS, UPLOAD_SUCCESS_MESSAGE, SECTION_HEADING, IS_EQUITY_ACCEPTED_PROOF,
  INITIAL_TOAST_DETAILS, INITIAL_UPLOAD_DETAILS, NO_RESULTS_FOUND,checkImageURL
} from './helpers/config';
import './bank-details-update.scss';
import PdfViewer from '../../../common/pdf-viewer';
import ZoomableImage from '../../../common/ZoomableImage';

const BankDetailsUpload = (props) => {
  const styles = {
    container: 'bank-details-update__container',
    table: 'bank-details-update__table',
    tableHeader: 'bank-details-update__table-header',
    tableRow: 'bank-details-update__table-row',
    tableRowImage: 'bank-details-update__table-row-image',
    tableRowImageRemove: 'bank-details-update__table-row-image-remove',
    buttonWrapper: 'bank-details-update__button-wrapper',
  };

  const [isLoading, setIsLoading] = React.useState(true);
  const [bankDetails, setBankDetails] = React.useState([]);
  const [isUploadingCheque, setIsUploadingCheque] = React.useState(false);
  const [toastDetails, setToastDetails] = React.useState(INITIAL_TOAST_DETAILS);
  const [uploadDetails, setUploadDetails] = React.useState(INITIAL_UPLOAD_DETAILS);

  const fetchBankDetails = () => {
    fetchUserBankDetails(BANK_FILTERS, props.customerId)
      .then((response) => setBankDetails(response.data))
      .catch((error) => setToastDetails({ show: true, message: error.error }))
      .finally(() => setIsLoading(false));
  };

  React.useEffect(() => {
    fetchBankDetails();
  }, []);

  const uploadCancelledCheque = () => {
    const params = {
      cancelledCheque: uploadDetails.file,
      isEquityAcceptedproof: uploadDetails.isEquityAcceptedproof,
      userId: props.customerId,
      bankId: uploadDetails.bankId,
    };

    setIsUploadingCheque(true);
    uploadBankCancelledCheque(params)
      .then(() => {
        setToastDetails({ show: true, message: UPLOAD_SUCCESS_MESSAGE });
        setUploadDetails(INITIAL_UPLOAD_DETAILS);
        setIsLoading(true);
        fetchBankDetails();
      })
      .catch((error) => setToastDetails({ show: true, message: error.error }))
      .finally(() => setIsUploadingCheque(false));
  };

  const updateSelectedFile = (e, bankId) => {
    const file = e.target.files[0];
    setUploadDetails({ ...uploadDetails, bankId, file });
  };

  const clearUploadDetails = () => {
    const fileUploadInputsObject = document.getElementsByClassName('bank-file-upload');
    const fileUploadInputsArrays = Array.prototype.slice.call(fileUploadInputsObject, 0);
    fileUploadInputsArrays.forEach((fileInput) => fileInput.value = '');
    setUploadDetails({ bankId: null, file: null, isEquityAcceptedproof: false });
  };

  const changeIsEquityAcceptedProofStatus = () => setUploadDetails({
    ...uploadDetails,
    isEquityAcceptedproof: !uploadDetails.isEquityAcceptedproof,
  });
  const getBankListView = () => (
    bankDetails.length === 0
      ? <h3>{NO_RESULTS_FOUND}</h3> : (
        <div>
          <table className={styles.table}>
            <th className={styles.tableHeader}>
              <td>{TABLE_HEADERS.SNO}</td>
              <td>{TABLE_HEADERS.DEFAULT_BANK}</td>
              <td>{TABLE_HEADERS.ACCOUNT_NUMBER}</td>
              <td>{TABLE_HEADERS.IFSC}</td>
              <td>{TABLE_HEADERS.PROOF}</td>
            </th>
            {bankDetails.map((bankAccount, index) => (
              <tr className={styles.tableRow}>
                <td>{index + 1}</td>
                <td>{bankAccount.isDefaultBankAccount ? 'YES' : 'NO'}</td>
                <td>{bankAccount.accountNumber}</td>
                <td>{bankAccount.ifsc}</td>
                <td>
                  <div className={styles.tableRowImage}>
                    { checkImageURL(bankAccount.cancelledChequePath) === true ?
                      <ZoomableImage
                        src={bankAccount.cancelledChequePath}
                        alt="bank-cheque"
                        baseWidth={450}
                        zoomWidth={600}
                      /> : <PdfViewer
                        url={bankAccount.cancelledChequePath}
                        className="account-opening-form"
                        withCredentials={false}
                      /> 
                    }
                  </div>
                  <input
                    type="file"
                    className="bank-file-upload"
                    disabled={uploadDetails.bankId && !(bankAccount.id === uploadDetails.bankId)}
                    onChange={(e) => updateSelectedFile(e, bankAccount.id)}
                  />
                  {bankAccount.id === uploadDetails.bankId && (
                  <div>
                    <PtmButton
                      className={styles.tableRowImageRemove}
                      handlePress={clearUploadDetails}
                      appearance="primaryDark"
                    >
                      Remove
                      {' '}
                      <i className="fa fa-times" />
                    </PtmButton>
                    <PtmCheckbox
                      checked={uploadDetails.isEquityAcceptedproof}
                      onChange={changeIsEquityAcceptedProofStatus}
                      id={bankAccount.id}
                      label={IS_EQUITY_ACCEPTED_PROOF}
                    />
                  </div>
                  )}
                </td>
              </tr>
            ))}
          </table>
          <div className={styles.buttonWrapper}>
            <PtmButton disabled={isUploadingCheque || !uploadDetails.file} appearance="primary" handlePress={uploadCancelledCheque}>
              Update
            </PtmButton>
          </div>
        </div>
      ));

  return (
    <div className={styles.container}>
      <PtmToast
        isVisible={toastDetails.show}
        message={toastDetails.message}
        changeState={() => setToastDetails(INITIAL_TOAST_DETAILS)}
      />
      <h1>{SECTION_HEADING}</h1>
      {isLoading ? <Loader.ContentLoader /> : getBankListView()}
    </div>
  );
};

BankDetailsUpload.propTypes = {
  customerId: PropTypes.string.isRequired,
  agentId: PropTypes.string.isRequired,
};

export default BankDetailsUpload;
