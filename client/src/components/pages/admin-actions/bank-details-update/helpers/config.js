const BANK_FILTERS = {
  filter: 'VERIFIED',
  verificationProofRequired: true,
  verificationMethod: 'BANKPROOF',
  isCrm: true,
};

const TABLE_HEADERS = {
  SNO: 'S. NO.',
  DEFAULT_BANK: 'MF Default Bank',
  ACCOUNT_NUMBER: 'Account Number',
  IFSC: 'IFSC',
  PROOF: 'PROOF',
};

const UPLOAD_SUCCESS_MESSAGE = 'File Uploaded Successfully!!';

const SECTION_HEADING = 'Update Bank Details';

const IS_EQUITY_ACCEPTED_PROOF = 'Is Equity Accepted Proof';

const NO_RESULTS_FOUND = 'No Results Found';

const INITIAL_TOAST_DETAILS = {
  show: false,
  message: '',
};

const INITIAL_UPLOAD_DETAILS = {
  bankId: null, file: null, isEquityAcceptedproof: false,
};
const checkImageURL = (url) => {
  if(!url) return true;
  return !url.includes("/getpdf");
}
export {
  BANK_FILTERS, TABLE_HEADERS, UPLOAD_SUCCESS_MESSAGE,
  SECTION_HEADING, IS_EQUITY_ACCEPTED_PROOF,
  INITIAL_TOAST_DETAILS, INITIAL_UPLOAD_DETAILS,
  NO_RESULTS_FOUND,
  checkImageURL,
};
