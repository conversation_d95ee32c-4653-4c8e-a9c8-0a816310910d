.bank-details-update {
  &__container {
    background: white;
    margin: 0 30px 30px 30px;
    padding: 30px;
    border-radius: 2px;
  }
  &__table {
    width: 100%;
    margin: 20px 0;
    &-header {
      display: flex;
      width: 100%;
      background: #3e4a59;
      & > * {
        color: white;
        flex: 1 0 14%;
        padding: 15px;
        border: 1px solid grey;
      }
      & > :last-child {
        flex: 1 0 44%;
      }
    }
    &-row {
      display: flex;
      width: 100%;
      &-image {
        width: 100%;
        height: auto;
        margin-bottom: 20px;
        &-remove {
          padding: 4px;
          margin: 10px 0;
        }
      }
      & > * {
         flex: 1 0 14%;
         padding: 15px;
         border: 1px solid grey;
      }
      & > :last-child {
        flex: 1 0 44%;
      }
    }
  }
  &__button-wrapper {
    display: flex;
    justify-content: center;
  }
}
