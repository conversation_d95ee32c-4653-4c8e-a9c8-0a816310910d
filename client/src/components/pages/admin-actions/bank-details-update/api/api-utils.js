import ApiService from '../../../../../services/ApiService';
import { ENDPOINTS } from '../../../../../constants/api-constants';

const fetchUserBankDetails = (params, customerId) => {
  const reqURL = ENDPOINTS.customer.getUserBankDetails;
  return ApiService.makeRequest(reqURL, params, customerId);
};

const uploadBankCancelledCheque = (params) => {
  const reqURL = ENDPOINTS.customer.uploadUserBankCancelledCheque;
  return ApiService.makeRequest(reqURL, params);
};

export {
  fetchUserBankDetails,
  uploadBankCancelledCheque,
};
