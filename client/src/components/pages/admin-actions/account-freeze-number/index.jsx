/* eslint-disable no-restricted-globals */
import React, { useEffect, useState } from 'react';
import { PtmButton, PtmInput, PtmToast } from '@paytm-money/frontend-common-v2';
import { getAccountFreezeNumber, updateAccountFreezeNumber } from './api';
import Loader from '../../../common/Loader';

const INITIAL_TOAST_DETAILS = {
  show: false,
  message: '',
};

const AccountFreezeNumber = () => {
  const [number, setNumber] = useState('');
  const [initialNumber, setInitialNumber] = useState('');
  const [isDisabled, setIsDisabled] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [toastDetails, setToastDetails] = React.useState(INITIAL_TOAST_DETAILS);

  const handleSubmit = () => {
    setIsDisabled(true);
    updateAccountFreezeNumber({ ivr_mobile_number: number }).then(() => {
      setToastDetails({
        show: true,
        message: 'IVR Mobile number updated successfully',
        containerClass: 'success',
      });
      setInitialNumber(number);
    }).catch((error) => {
      setToastDetails({
        show: true,
        message: error.error || 'Something went wrong',
        containerClass: 'error',
      });
    }).finally(() => setIsDisabled(false));
  };

  const onNumberChange = (e) => {
    const { value } = e.target;
    if (!isNaN(value)) {
      setNumber(value);
    }
  };

  useEffect(() => {
    setIsLoading(true);
    getAccountFreezeNumber().then((response) => {
      setNumber(response.ivr_mobile_number);
      setInitialNumber(response.ivr_mobile_number);
    }).catch(() => {}).finally(() => setIsLoading(false));
  }, []);

  if (isLoading) {
    return (<Loader.ContentLoader />);
  }

  return (
    <div id="main-container" className="full-height">
      <PtmToast
        isVisible={toastDetails.show}
        message={toastDetails.message}
        containerClass={toastDetails.containerClass}
        changeState={() => setToastDetails(INITIAL_TOAST_DETAILS)}
      />
      <div className="push-100-r push-100-l push-100-t">
        <h4 className="push-20"> Account Freeze Number </h4>
        <div className="bg-white pin-form-content">
          <h5 className="pad-10-l pad-10-r pad-10 pad-10-t">
            Current Number:
            {' '}
            {initialNumber}
          </h5>
          <div className="pad-10-l pad-10-r pad-10">
            <PtmInput
              id="ivr-number"
              showLabel
              roundedCorners
              name="ivr-number"
              label="IVR Mobile Number"
              placeholder="Please enter IVR number"
              onChange={onNumberChange}
              value={number}
            />
          </div>
          <div className="pad-10-l pad-10 push-20-t">
            <PtmButton
              appearance="primary"
              handlePress={handleSubmit}
              disabled={isDisabled}
            >
              Save
            </PtmButton>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountFreezeNumber;
