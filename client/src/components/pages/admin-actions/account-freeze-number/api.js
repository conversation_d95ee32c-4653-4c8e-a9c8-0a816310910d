import ApiService from '../../../../services/ApiService';
import { ENDPOINTS } from '../../../../constants/api-constants';

const getAccountFreezeNumber = () => {
  return ApiService.makeRequest(ENDPOINTS.adminActions.getAccountFreezeNumber);
};

const updateAccountFreezeNumber = (body) => {
  const reqURL = ENDPOINTS.adminActions.updateAccountFreezeNumber;
  return ApiService.makeRequest(reqURL, body);
};

export {
  getAccountFreezeNumber,
  updateAccountFreezeNumber,
};
