import React, { useState } from 'react';
import {
  PtmButton, PtmToast
} from '@paytm-money/frontend-common-v2';

import UploadFileButton from '../../../common/UploadFileButton';

import 'rc-tooltip/assets/bootstrap_white.css';
import './index.scss';

import { 
  submitCersiaKinReferenceUpload,
  submitKinBulkDownloadLog,
  submitKinMisReportUpload
} from './api-utils';

const INITIAL_TOAST_DETAILS = {
    message: '',
    class: '',
    isVisible: false,
};

const CersiaKinReferenceUpload = () => {
  const [cersiaKinReferenceFile, saveCersiaKinReferenceFile] = useState(null);
  const [kinBulkDownloadLogFile, saveKinBulkDownloadLogFile] = useState(null);
  const [kinMisReportFile, saveKinMisReportFile] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [toastDetails, setToastDetails] = useState(INITIAL_TOAST_DETAILS);

  const saveCersiaFile = (fileInput) => {
    saveCersiaKinReferenceFile(fileInput);
  };

  const saveKinBulkDownloadFile = (fileInput) => {
    saveKinBulkDownloadLogFile(fileInput);
  };

  const saveKinMisFile = (fileInput) => {
    saveKinMisReportFile(fileInput);
  };

  const submitCersiaKinReferenceFile = () => {
    const params = {
      file: cersiaKinReferenceFile,
    };
    setIsLoading(true);
    submitCersiaKinReferenceUpload(params)
      .then(() => {
        setToastDetails({
          message: 'CKYC Build Response File uploaded successfully',
          className: 'success',
          isVisible: true,
        });
      })
      .catch((error) => setToastDetails({
        // eslint-disable-next-line no-nested-ternary
        message: error.meta ? error.meta.displayMessage : error.error ? error.error : 'something went wrong',
        class: 'error',
        isVisible: true,
      }))
      .finally(() => {
        setIsLoading(false);
        saveCersiaKinReferenceFile(null);
      });
  };

  const submitKinBulkDownloadLogFile = () => {
    const params = {
      file: kinBulkDownloadLogFile,
    };
    setIsLoading(true);
    submitKinBulkDownloadLog(params)
      .then(() => {
        setToastDetails({
          message: 'KIN Bulk Download Log uploaded successfully',
          className: 'success',
          isVisible: true,
        });
      })
      .catch((error) => setToastDetails({
        // eslint-disable-next-line no-nested-ternary
        message: error.meta ? error.meta.displayMessage : error.error ? error.error : 'something went wrong',
        class: 'error',
        isVisible: true,
      }))
      .finally(() => {
        setIsLoading(false);
        saveKinBulkDownloadLogFile(null);
      });
  };

  const submitKinMisReportFile = () => {
    const params = {
      file: kinMisReportFile,
    };
    setIsLoading(true);
    submitKinMisReportUpload(params)
      .then(() => {
        setToastDetails({
          message: 'KIN MIS Report uploaded successfully',
          className: 'success',
          isVisible: true,
        });
      })
      .catch((error) => setToastDetails({
        // eslint-disable-next-line no-nested-ternary
        message: error.meta ? error.meta.displayMessage : error.error ? error.error : 'something went wrong',
        class: 'error',
        isVisible: true,
      }))
      .finally(() => {
        setIsLoading(false);
        saveKinMisReportFile(null);
      });
  };

  const closeToast = () => setToastDetails(INITIAL_TOAST_DETAILS);

  return (
    <div id="main-container">
      <PtmToast
        isVisible={toastDetails.isVisible}
        message={toastDetails.message}
        containerClass={toastDetails.class}
        changeState={closeToast}
      />
      <div className="remove-margin-b">
        <div className="content pad-50-b bg-gray-lighter">
          <h1 className="page-heading">Cersia KIN Reference Upload</h1>
        </div>
      </div>
      <div className="lead-upload-container cersia-kin-reference-upload-container">
        
        {/* CKYC Build Response File Upload */}
        <div className="upload-section">
          <UploadFileButton
            label="CKYC Build Response File"
            fileUpload={saveCersiaFile}
            acceptType=".txt"
          />
          <div>
            <span>
              {cersiaKinReferenceFile ? 1 : 0}
              {' '}
              file uploaded
            </span>
          </div>
          <PtmButton 
            appearance="primary" 
            handlePress={submitCersiaKinReferenceFile} 
            disabled={!(cersiaKinReferenceFile) || isLoading}
          >
            Submit CKYC
          </PtmButton>
        </div>

        {/* KIN Bulk Download Log Upload */}
        <div className="upload-section">
          <UploadFileButton
            label="KIN Bulk Download Log"
            fileUpload={saveKinBulkDownloadFile}
            acceptType=".xlsx"
          />
          <div>
            <span>
              {kinBulkDownloadLogFile ? 1 : 0}
              {' '}
              file uploaded
            </span>
          </div>
          <PtmButton 
            appearance="primary" 
            handlePress={submitKinBulkDownloadLogFile} 
            disabled={!(kinBulkDownloadLogFile) || isLoading}
          >
            Submit Download Log
          </PtmButton>
        </div>

        {/* KIN MIS Report Upload */}
        <div className="upload-section">
          <UploadFileButton
            label="KIN MIS Report Upload"
            fileUpload={saveKinMisFile}
            acceptType=".xlsx"
          />
          <div>
            <span>
              {kinMisReportFile ? 1 : 0}
              {' '}
              file uploaded
            </span>
          </div>
          <PtmButton 
            appearance="primary" 
            handlePress={submitKinMisReportFile} 
            disabled={!(kinMisReportFile) || isLoading}
          >
            Submit MIS Report
          </PtmButton>
        </div>

      </div>
    </div>
  );
};

export default CersiaKinReferenceUpload;
