import ApiService from '../../../../services/ApiService';
import { ENDPOINTS } from '../../../../constants/api-constants';

const submitCersiaKinReferenceUpload = (params) => {
  const reqObj = ENDPOINTS.adminActions.submitCersiaKinReferenceUpload;
  return ApiService.makeRequest(reqObj, params);
};

const submitKinBulkDownloadLog = (params) => {
  const reqObj = ENDPOINTS.adminActions.submitKinBulkDownloadLog;
  return ApiService.makeRequest(reqObj, params);
};

const submitKinMisReportUpload = (params) => {
  const reqObj = ENDPOINTS.adminActions.submitKinMisReportUpload;
  return ApiService.makeRequest(reqObj, params);
};

export { 
  submitCersiaKinReferenceUpload,
  submitKinBulkDownloadLog,
  submitKinMisReportUpload
};
