import ApiService from '../../../../../services/ApiService';
import { ENDPOINTS } from '../../../../../constants/api-constants';

const fetchRejectReasonData = () => ApiService.makeRequest(ENDPOINTS.adminActions.getRejectionFieldName);
const callApiCreateRejectReason = (payload) => {
  return ApiService.makeRequest(ENDPOINTS.adminActions.createRejectReason, payload);
};
const callApiUpdateRejectReason = (payload) => {
  return ApiService.makeRequest(ENDPOINTS.adminActions.updateRejectReason, payload);
};

export {
  fetchRejectReasonData,
  callApiCreateRejectReason,
  callApiUpdateRejectReason,
};
