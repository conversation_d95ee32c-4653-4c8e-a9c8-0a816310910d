import React from 'react';
import PropTypes from 'prop-types';
import Select from 'react-select';

const FilterSelection = (props) => {
  const {
    fieldName,
    category,
    onChange,
    handleReasonChange,
    addReasonBlock,
    selectedCategory,
    selectedFieldName,
  } = props;

  const reasonOperation = {
    ADD_REASON: 'Add Reason',
    EDIT_REASON: 'Edit Reason',
  };

  return (
    <section className="">
      <div className="clearfix push-30 push-20-t">
        <div className="col-md-5">
          <p>Field Name</p>
          <Select
            onChange={(params) => {
              onChange('rejectCode', params);
            }}
            options={fieldName}
            name="fieldName"
            value={{ label: selectedFieldName, value: selectedFieldName }}
          />
        </div>
        <div className="col-md-5">
          <p>Category</p>
          <Select
            onChange={(params) => {
              onChange('category', params);
            }}
            options={category}
            name="category"
            value={{ label: selectedCategory, value: selectedCategory }}
          />
        </div>
      </div>
      <div className="push-20-l push-30">
        <label className="push-50-r">
          <input
            name="reasonAction"
            className="push-10-r"
            type="radio"
            value={reasonOperation.ADD_REASON}
            onChange={e => handleReasonChange(e.target.value)}
            checked={addReasonBlock}
          />
          Add Reason
        </label>
        <label className="push-50-r">
          <input
            name="reasonAction"
            className="push-10-r"
            type="radio"
            value={reasonOperation.EDIT_REASON}
            checked={!addReasonBlock}
            onChange={e => handleReasonChange(e.target.value)}
          />
          Edit Reason
        </label>
      </div>
    </section>
  );
};

FilterSelection.propTypes = {
  fieldName: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.array,
  ]).isRequired,
  category: PropTypes.array.isRequired,
  onChange: PropTypes.func.isRequired,
  handleReasonChange: PropTypes.func.isRequired,
  addReasonBlock: PropTypes.bool.isRequired,
  selectedCategory: PropTypes.string.isRequired,
  selectedFieldName: PropTypes.string.isRequired,
};
export default FilterSelection;
