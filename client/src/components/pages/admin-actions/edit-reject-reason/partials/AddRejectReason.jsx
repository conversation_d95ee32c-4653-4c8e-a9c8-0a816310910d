import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { PtmInput, PtmToggle, PtmButton } from '@paytm-money/frontend-common-v2';

import { callApiCreateRejectReason } from '../api-util';

const initState = {
  createDisplayReason: '',
  createRejectReason: '',
  isActiveReason: false,
};

class AddRejectReason extends Component {
  constructor(props) {
    super(props);
    this.state = {
      message: '',
      ...initState,
    };
  }

  onAddReasonInputChange = (e) => {
    if (e.target.name === 'addRejectReason') {
      this.setState({ createRejectReason: e.target.value });
    } else if (e.target.name === 'addDisplayReason') {
      this.setState({ createDisplayReason: e.target.value });
    }
  }

  onPressCreateRejectReason =() => {
    let { payload } = this.props;
    const { createDisplayReason, createRejectReason, isActiveReason } = this.state;
    payload = {
      ...payload,
      agentReason: createRejectReason,
      displayReason: createDisplayReason,
      active: isActiveReason,
    };
    callApiCreateRejectReason(payload).then((response) => {
      this.setState({
        message: response.meta.message,
        ...initState,
      });
    }).catch((error) => {
      this.setState({
        message: error.error,
        ...initState,
      });
    });
  }

  handleStatusChange(target) {
    target === true ? this.setState({ isActiveReason: true })
      : this.setState({ isActiveReason: false });
  }

  render() {
    const {
      createRejectReason, createDisplayReason, message, isActiveReason,
    } = this.state;
    const { payload: { category, field_name } } = this.props;
    const enableCreateBtn = (createRejectReason && createDisplayReason && category && field_name) === '';
    return (
      <section>
        <div className="col-md-12 push-30">
          <PtmInput
            id="addRejectReason"
            label="Rejection Reason in CRM"
            onChange={(e) => this.onAddReasonInputChange(e)}
            name="addRejectReason"
            value={createRejectReason}
            borderless
          />
        </div>
        <div className="col-md-12 push-30">
          <PtmInput
            id="addDisplayReason"
            label="Display Reason for Customer"
            onChange={(e) => this.onAddReasonInputChange(e)}
            name="addDisplayReason"
            value={createDisplayReason}
            borderless
          />
        </div>
        <div className="col-md-12 push-30">
          <PtmToggle
            switchType="round"
            onChange={(e) => this.handleStatusChange(e.target.checked)}
            active={isActiveReason}
            label="Rejection Reason Status"
            labelClass="font-s16"
          />
          <div>
            <PtmButton
              className="push-30-t"
              handlePress={this.onPressCreateRejectReason}
              disabled={enableCreateBtn}
              appearance="primaryDark"
            >
              Create
            </PtmButton>
          </div>
          <p className="col-sm-9 push-10-t">{message}</p>
        </div>
      </section>
    );
  }
}

AddRejectReason.propTypes = {
  payload: PropTypes.object.isRequired,
};

export default AddRejectReason;
