import React, { Component } from 'react';
import PropTypes from 'prop-types';
import Select from 'react-select';
import { PtmInput, PtmToggle, PtmButton } from '@paytm-money/frontend-common-v2';

import isEmpty from 'lodash/isEmpty';
import { callApiUpdateRejectReason } from '../api-util';

const initState = {
  editedDisplayReason: '',
  editedRejectReason: '',
  isActiveReason: false,
};
class EditReason extends Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedField: {},
      message: '',
      ...initState,
    };
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.agentRejectReasonList.length === 0) {
      this.setState({
        selectedField: {},
      });
    }
  }

  onSelectRejectReason = (obj) => {
    const { agentRejectReasonList } = this.props;
    const selectedField = agentRejectReasonList.find((i) => i.agentReason === obj.value);
    this.setState({
      selectedField,
      isActiveReason: selectedField.active,
    });
  }

  onDisplayReasonChange = (event) => {
    this.setState({ editedDisplayReason: event.target.value });
  }

  onRejectReasonChange = (event) => {
    this.setState({ editedRejectReason: event.target.value });
  }

  updateRejectReason = () => {
    let { payload } = this.props;
    const {
      editedDisplayReason, editedRejectReason, selectedField, isActiveReason,
    } = this.state;
    payload = {
      ...payload,
      agentReason: editedRejectReason,
      displayReason: editedDisplayReason,
      active: isActiveReason,
      id: selectedField.id,
    };
    callApiUpdateRejectReason(payload).then((response) => {
      this.setState({
        message: response.message,
        ...initState,
      });
    }).catch((error) => {
      this.setState({
        message: error.error,
        ...initState,
      });
    });
  }

  handleStatusChange(target) {
    target === true ? this.setState({ isActiveReason: true })
      : this.setState({ isActiveReason: false });
  }

  render() {
    const { agentRejectReasonList, payload } = this.props;
    const { category, field_name } = payload;
    const {
      editedDisplayReason, editedRejectReason, selectedField, message, isActiveReason,
    } = this.state;
    const enableUpdateBtn = ((editedDisplayReason && editedRejectReason && category && field_name) === '') || isEmpty(selectedField);
    return (
      <section>
        <div className="clearfix">
          <div className="col-md-5">
            <label htmlFor="material-text"> Rejection reason in CRM </label>
            <Select
              options={agentRejectReasonList}
              onChange={this.onSelectRejectReason}
              value={selectedField}
            />
          </div>
        </div>
        <div className="block-content">
          <label htmlFor="material-text"> Display reason for customer</label>
          <p className="push-10-t">
            {selectedField.displayReason}
          </p>
        </div>
        <div className="block-content">
          <PtmInput
            id="editRejectCrm"
            label="Edit Rejection Reason in CRM"
            onChange={this.onRejectReasonChange}
            value={editedRejectReason}
            name=""
            borderless
          />
          <PtmInput
            id="editRejectCustomer"
            onChange={this.onDisplayReasonChange}
            label="Edit Display Reason for Customer"
            value={editedDisplayReason}
            name=""
            borderless
          />
        </div>
        <div className="block-content">
          <PtmToggle
            switchType="round"
            onChange={(e) => this.handleStatusChange(e.target.checked)}
            active={isActiveReason}
            label="Rejection Reason Status"
            labelClass="font-s16"
          />
        </div>
        <div className="block-content">
          <PtmButton
            appearance="primaryDark"
            handlePress={this.updateRejectReason}
            className="block-content"
            disabled={enableUpdateBtn}
          >
            Update
          </PtmButton>
        </div>
        <p className="push-10-t push-20-l">{message}</p>
      </section>
    );
  }
}

EditReason.propTypes = {
  agentRejectReasonList: PropTypes.array.isRequired,
  payload: PropTypes.object.isRequired,
};

export default EditReason;
