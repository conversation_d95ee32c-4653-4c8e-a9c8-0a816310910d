import React, { Component } from 'react';
import autobind from 'autobind-decorator';
import PropTypes from 'prop-types';
import AddRejectReason from './partials/AddRejectReason';
import EditReason from './partials/EditRejectReason';
import FilterSelection from './partials/FilterSelection';
import { fetchRejectReasonData } from './api-util';
import { getValuesFromNestedArray } from '../../../../util';
import Loader from '../../../common/Loader';

export const reasonOperation = {
  ADD_REASON: 'Add Reason',
  EDIT_REASON: 'Edit Reason',
};

export const reasonLevel = {
  FIELDNAME: 'rejectCode',
  CATEGORY: 'category',
};

const initState = {
  fieldNameData: [],
  selectedFieldName: '',
  selectedCategory: '',
  fieldName: [],
  category: [],
  agentRejectReasonList: [],
  addReasonBlock: true,
  error: null,
  loading: false,
};

class EditRejectReasonContainer extends Component {
  constructor(props) {
    super(props);
    this.state = {
      ...initState,
    };
  }

  componentDidMount() {
    const { addReasonBlock } = this.state;
    this.getFilterValues(addReasonBlock);
  }

  @autobind
  onChange(name, params) {
    const { value } = params;
    const { selectedFieldName, fieldNameData } = this.state;
    let options = { fieldNameData };
    const { FIELDNAME, CATEGORY } = reasonLevel;
    if (name === FIELDNAME) {
      options = { ...options, filterBy: value, type: 'fieldName' };
      const rejectCategory = getValuesFromNestedArray(options);
      this.setState({
        selectedFieldName: value,
        category: rejectCategory,
        agentRejectReasonList: [],
        selectedCategory: '',
      });
    } else if (name === CATEGORY) {
      options = { ...options, filterBy: selectedFieldName, type: 'category', value };
      const resultList = getValuesFromNestedArray(options);
      this.setState({
        selectedCategory: value,
        agentRejectReasonList: resultList,
      });
    }
  }

  @autobind
  getFilterValues(val) {
    this.setState({ loading: true });
    fetchRejectReasonData().then((response) => {
      const rejectReason = Object.keys(response).map(ele => ({
        label: ele,
        value: ele,
      }));
      this.setState({
        ...initState,
        fieldName: rejectReason,
        fieldNameData: response,
        addReasonBlock: val,
        loading: false, 
      });
    }).catch(({ error }) => {
      this.setState({
        ...initState,
        error: { message: error || 'Something went wrong' }
      });
    }).finally(() => {
      this.setState({ loading: false });
    });
  }

  @autobind
  handleReasonChange(value) {
    const { ADD_REASON, EDIT_REASON } = reasonOperation;
    if (value === ADD_REASON) {
      this.getFilterValues(true); // enable add reason view
    } else if (value === EDIT_REASON) {
      this.getFilterValues(false); // disable add reason view and enable edit reason view
    }
  }

  render() {
    const { selectedFieldName, selectedCategory, addReasonBlock, category, fieldName, loading, error } = this.state;
    const { agentId } = this.props;
    const addRejectReasonPayload = {
      category: selectedCategory,
      field_name: selectedFieldName,
    };
    return (
      <main id="main-container" className="full-height">
        <div className="remove-margin-b">
          <div className="content pad-50-b bg-gray-lighter">
            <h1 className="page-heading">Edit reject reason</h1>
          </div>
        </div>
        { loading ? (
          <Loader.SectionLoader />
        ) : (
          <div className="block container-fluid" style={{ height: '95vh' }}>
            { error && error.message && (
              <div className="block-content">
                <p>
                  {error.message}
                </p>
              </div>
            )}
            <FilterSelection
              fieldName={fieldName}
              selectedFieldName={selectedFieldName}
              onChange={this.onChange}
              category={category}
              selectedCategory={selectedCategory}
              handleReasonChange={this.handleReasonChange}
              addReasonBlock={addReasonBlock}
            />
            {
              addReasonBlock && (
                <AddRejectReason
                  payload={addRejectReasonPayload}
                />
              )
            }
            {
              addReasonBlock !== null && !addReasonBlock && (
                <EditReason
                  agentRejectReasonList={this.state.agentRejectReasonList}
                  payload={addRejectReasonPayload}
                />
              )
            }
          </div>
        )
        }
      </main>
    );
  }
}

EditRejectReasonContainer.propTypes = {
  agentId: PropTypes.number.isRequired,
};

export default EditRejectReasonContainer;
