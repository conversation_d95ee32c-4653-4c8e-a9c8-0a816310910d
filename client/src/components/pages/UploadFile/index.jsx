import React, { useEffect, useState } from 'react';
import './index.css';
import { PtmToast, PtmBootstrapTable } from '@paytm-money/frontend-common-v2';
import { TableHeaderConfig } from './constants';
import Loader from '../../common/Loader';
import {
  getCampaignList, deleteFile, downloadFile, uploadFile,
} from './api';
import { formatTableData } from './utils';

export default function UploadFile() {
  const [tableContent, setTableContent] = useState({});
  const [isLoading, setLoading] = useState(true);
  const [file, setFile] = useState('');
  const [fileName, setFileName] = useState('Choose File');
  const [message, setMessage] = useState('');
  const [progress, setProgress] = useState(0);
  const [pageNumber, setPageNumber] = useState(1);
  const [total, setTotal] = useState([]);
  const [count, setCount] = useState(0);
  const [success, setSuccess] = useState(false);
  useEffect(() => {
    getCampaignList().then((response) => {
      const [data, len] = formatTableData(response);
      setTableContent(data[0] || data);
      setCount(len);
      setTotal(data);
      setLoading(false);
    }).catch((error) => {
      setSuccess(false);
      setMessage(error);
    });
  }, [message]);
  function handleDelete(name) {
    const params = {
      file: name,
    };
    deleteFile(params)
      .then((res) => {
        setSuccess(false);
        setMessage(res.status);
      })
      .catch((error) => {
        setSuccess(false);
        setMessage(error);
      });
    setPageNumber(1);
  }
  function onChange(e) {
    if (e.target.files[0]) {
      setFile(e.target.files[0]);
      setFileName(e.target.files[0].name);
    }
  }
  async function onSubmit(e) {
    e.preventDefault();
    const formData = new FormData();
    formData.append('file', file);
    setProgress(1);
    uploadFile(formData)
      .then((res) => {
        setSuccess(true);
        setMessage(res.status);
        setProgress(0);
      })
      .catch((error) => {
        setSuccess(false);
        setMessage(error);
        setProgress(0);
      });
    setFileName('Choose File');
    setFile('');
    setPageNumber(1);
    document.getElementById('inputfile').value = '';
  }
  function handleClick(e) {
    downloadFile(e)
      .then((res) => {
        const blob = new Blob([res], { type: 'text/csv;charset=utf-8;' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.visibility = 'hidden';
        a.href = url;
        a.download = e;
        document.body.appendChild(a);
        a.click();
        a.remove();
      })
      .catch((error) => {
        setMessage(error);
        setSuccess(false);
      });
  }

  return (
    <div style={{ backgroundColor: 'white' }}>
      <div style={{ height: 100 }} />
      <form onSubmit={(e) => onSubmit(e)}>
        <PtmToast
          message={message}
          isVisible={!!message}
          changeState={() => setMessage('')}
          containerClass={success ? 'success' : 'error'}
        />
        <div id="container">
          <input
            type="file"
            id="inputfile"
            onChange={onChange}
            accept=".csv"
          />
          <div>
            <label id="label" htmlFor="inputfile">
              {fileName}
            </label>
          </div>
        </div>
        { progress === 1 && (
          <div className="button-loader">
            <Loader.BtnGroupLoader />
          </div>
        )}
        <input
          type="submit"
          style={{ backgroundColor: fileName === 'Choose File' || progress === 1 ? '#989fa3' : '#494c4e' }}
          value={progress ? 'Uploading' : 'Upload'}
          id="button"
          disabled={fileName === 'Choose File' || progress === 1}
        />
      </form>
      <br />
      <br />
      <div>
        {isLoading ? <Loader.SectionLoader /> : (
          <PtmBootstrapTable
            keyField="Campaign Name"
            pagination
            headerConfigArr={TableHeaderConfig(handleClick, handleDelete)}
            data={tableContent || null}
            totalCount={count}
            sizePerPage={6}
            pageNumber={pageNumber}
            onChangePageNumber={(page) => { setTableContent(total[page - 1]); setPageNumber(page); }}
          />
        )}
      </div>
    </div>

  );
}
