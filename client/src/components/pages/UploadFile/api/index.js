import ApiService from '../../../../services/ApiService';
import { ENDPOINTS } from '../../../../constants/api-constants';

const getCampaignList = () => {
  const reqObj = ENDPOINTS.userCampaign.getCampaignList;
  return ApiService.makeRequest(reqObj);
};
const deleteFile = (name) => {
  const reqObj = ENDPOINTS.userCampaign.deleteFile;
  return ApiService.makeRequest(reqObj, name);
};
const downloadFile = (name) => {
  const reqObj = ENDPOINTS.userCampaign.downloadFile;
  return ApiService.makeRequest(reqObj, name);
};
const uploadFile = (file) => {
  const reqObj = ENDPOINTS.userCampaign.uploadFile;
  const headers = { 'Content-Type': 'multipart/form-data' };
  return ApiService.makeRequest(reqObj, file, null, headers);
};

export {
  getCampaignList,
  deleteFile,
  downloadFile,
  uploadFile,
};
