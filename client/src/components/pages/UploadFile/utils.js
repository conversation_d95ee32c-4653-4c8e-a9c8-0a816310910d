import moment from 'moment';

function changeFormat(date1) {
  let newDate = date1.replace(/ /g, '');
  newDate = newDate.slice(0, 10);
  newDate = newDate.replace(/-/g, '');
  return (moment(newDate, 'DDMMyyyy').format('MM/DD/yyyy'));
}
export function formatTableData(response) {
  const itemList = response;
  let sortedList = itemList.filter((item) => item['Validity To'] && item['Validity To'].trim()).sort((item1, item2) => (new Date(changeFormat(item2['Validity To'])) - new Date(changeFormat(item1['Validity To']))));
  sortedList = [...sortedList, ...itemList.filter((item) => !item['Validity To'] || !item['Validity To'].trim())];
  const result = sortedList.reduce((all, one, i) => {
    const ch = Math.floor(i / 6);
    all[ch] = [...(all[ch] || []), one];
    return all;
  }, []);
  return ([result, itemList.length]);
}
