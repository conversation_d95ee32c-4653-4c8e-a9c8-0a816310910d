import React from 'react';

const TableHeaderConfig = (handleClick, handleDelete) => [
  {
    title: 'Campaign Name',
    dataField: 'Campaign Name',
    width: '14%',
  }, {
    title: 'Campaign ID',
    dataField: 'Campaign ID',
    width: '6%',
  }, {
    title: 'Campaign Description',
    dataField: 'Campaign Description',
    width: '14%',
  }, {
    title: 'Validity From',
    dataField: 'Validity From',
    width: '6%',
  }, {
    title: 'Expiry',
    dataField: 'Validity To',
    width: '5%',
  }, {
    title: 'Month of Campaign',
    dataField: 'Month of Campaign',
    width: '6%',
  }, {
    title: 'Gratification done',
    dataField: 'Gratification done',
    width: '8%',
  }, {
    title: 'Date of gratification',
    dataField: 'Date of gratification',
    width: '5%',
  }, {
    title: 'Benefit Condition',
    dataField: 'Benefit Condition',
    width: '14%',
  }, {
    title: 'SPOC Name',
    dataField: 'SPOC Name',
    width: '5%',
  }, {
    title: 'SPOC Emp ID',
    dataField: 'SPOC Emp ID',
    width: '5%',
  }, {
    title: 'Attachment file',
    dataField: 'Attachment file',
    width: '7%',
    dataFormat: (Attachment) => (
      <div onClick={() => handleClick(Attachment)} style={{ textAlign: 'center', cursor: 'pointer', fontWeight: 'bold' }}>
        {Attachment}
      </div>
    ),
  },
  {
    title: 'Action',
    dataField: 'Attachment file',
    width: '5%',
    dataFormat: (Attachment) => (
      <div onClick={() => handleDelete(Attachment)} style={{ cursor: 'pointer', color: 'red', fontWeight: 'bold' }}>
        Delete
      </div>
    ),
  },
];

export { TableHeaderConfig };
