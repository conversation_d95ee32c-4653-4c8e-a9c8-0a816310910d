import React from 'react';
import PropTypes from 'prop-types';
import MaskComponent from './partials/MaskComponent';
import SearchBlock from './partials/SearchBlock';

const AadharMasking = (props) => {
  const {
    docData, searchBlockProps, customerId, onSelectPoiFile, onMaskComplete, searchVisible,
  } = props;

  const maskProps = {
    docData,
    customerId,
    onSelectPoiFile,
    onMaskComplete,
  };

  return (
    <>
      <h2 className="h2 push-10-t push-20">
        Aadhar Masking
      </h2>
      {searchVisible ? <SearchBlock { ...searchBlockProps } /> : (
        <MaskComponent { ...maskProps } />
      )}
    </>
  );
};

AadharMasking.propTypes = {
  docData: PropTypes.object.isRequired,
  searchBlockProps: PropTypes.object.isRequired,
  customerId: PropTypes.string.isRequired,
  onSelectPoiFile: PropTypes.func.isRequired,
  onMaskComplete: PropTypes.func.isRequired,
  searchVisible: PropTypes.bool.isRequired,
};

export default AadharMasking;
