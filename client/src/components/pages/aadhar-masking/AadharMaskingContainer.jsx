import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { PtmToast } from '@paytm-money/frontend-common-v2';
import { getDocuments, uploadCoreMask } from './api-util';
import './styles.scss';
import { getQueryParams } from '../../../util';
import Loader from '../../common/Loader';
import AadharMasking from './AadharMasking';
import { getFileInfoFromBlob, getFileInfoFromSelectedFile } from '../customer-details/customer-details-utils';
import { getProductInfo } from './config';

const AadharMaskingContainer = (props) => {
  const { history, agentId } = props;
  const [searchVal, setSearchVal] = useState('');
  const [docData, setDocData] = useState({});
  const [selectedPoiFile, setSelectedPoiFile] = useState(null);
  const [renderScreen, setRenderScreen] = useState(false);
  const [toastState, setToastState] = useState({
    showToast: false,
    errorMsg: '',
    isError: false,
  });
  const [searchInProgress, setSearchInProgress] = useState(false);
  const [searchVisible, setSearchVisible] = useState(true);

  const fetchCustomerIdFromUrl = (url) => {
    const queryObj = getQueryParams(url);
    return queryObj.customerId || null;
  };

  const buildFormObject = (file, productObj, index) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('docCode', 'AADHAAR');
    formData.append('docValue', index === 99 ? 'BA' : 'FA');
    formData.append('userId', searchVal);
    formData.append('isCore', productObj.isCore);
    formData.append('product', productObj.product);
    formData.append('subProduct', productObj.subProduct);
    return formData;
  };

  const onSelectPoiFile = (e) => {
    const file = e.target.files[0];
    setSelectedPoiFile(file);
  };

  const searchCustomer = (userId) => {
    const params = {
      userId,
    };
    setSearchInProgress(true);
    getDocuments(params)
      .then((response) => {
        setDocData(response);
        setSearchVisible(false);
      })
      .catch((error) => {
        setToastState({
          errorMsg: error.error || 'Something went wrong',
          isError: true,
          showToast: true,
        });
      })
      .finally(() => {
        setSearchInProgress(false);
        history.push(`/aadhar-masking?customerId=${userId}`);
      });
  };

  const onMaskComplete = (index = 0, blobObject = null, product) => {
    let fileInfo;
    if (blobObject) {
      fileInfo = getFileInfoFromBlob(blobObject);
    }

    if (selectedPoiFile && !fileInfo) {
      fileInfo = getFileInfoFromSelectedFile(selectedPoiFile);
    }

    if (!fileInfo) {
      return;
    }

    const { file } = fileInfo;
    const params = buildFormObject(file, getProductInfo(product), index);
    setSearchInProgress(true);
    uploadCoreMask(params)
      .then(() => {
        setRenderScreen(!renderScreen);
      })
      .catch((error) => {
        setToastState({
          errorMsg: error.error || 'Something went wrong',
          isError: true,
          showToast: true,
        });
      })
      .finally(() => setSearchInProgress(false));
  };

  const searchUser = () => {
    searchCustomer(searchVal);
  };

  useEffect(() => {
    if (history.location.search && !(history.location.search.includes('__proto__'))) {
      const custId = fetchCustomerIdFromUrl(history.location.search);
      if (custId) {
        searchCustomer(custId);
        setSearchVal(custId);
      } else {
        history.push('/aadhar-masking');
      }
    }
  }, [history, renderScreen]);

  const { errorMsg, isError, showToast } = toastState;

  const changeState = () => {
    setToastState((prevState) => ({
      ...prevState,
      showToast: false,
    }));
  };

  const searchBlockProps = {
    searchVal,
    setSearchVal,
    searchUser,
  };

  const childProps = {
    searchBlockProps,
    docData,
    customerId: searchVal,
    onSelectPoiFile,
    onMaskComplete,
    searchVisible,
  };

  return (
    <div className="content aadhar-masking-container">
      <PtmToast
        isVisible={showToast}
        message={errorMsg}
        containerClass={!isError ? 'success' : 'error'}
        changeState={changeState}
      />
      {searchInProgress ? <Loader.ContentLoader /> : <AadharMasking {...childProps} />}
    </div>
  );
};

AadharMaskingContainer.propTypes = {
  history: PropTypes.object.isRequired,
  agentId: PropTypes.number.isRequired,
};

export default AadharMaskingContainer;
