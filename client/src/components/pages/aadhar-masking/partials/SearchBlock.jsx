import React from 'react';
import PropTypes from 'prop-types';
import Button from '../../../common/ActionButton';

const SearchBlock = (props) => {
  const { searchVal, setSearchVal, searchUser } = props;

  const handleFormSubmission = (e) => {
    e.preventDefault();
    searchUser();
  };

  return (
    <div className="push-100-r push-50-t">
      <form onSubmit={handleFormSubmission}>
        <div className="input-group input-group-lg push-30">
          <input
            type="text"
            className="form-control"
            value={searchVal}
            placeholder="Search using Customer ID"
            onChange={(e) => setSearchVal(e.target.value)}
          />
          <div className="input-group-btn">
            <Button.IconButton
              iconClass="fa fa-search"
              onClickAction={searchUser}
            />
          </div>
        </div>
      </form>
    </div>
  );
};

SearchBlock.propTypes = {
  searchVal: PropTypes.string.isRequired,
  searchUser: PropTypes.func.isRequired,
  setSearchVal: PropTypes.func.isRequired,
};

export default SearchBlock;
