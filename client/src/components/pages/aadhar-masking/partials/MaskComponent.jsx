import React from 'react';
import PropTypes from 'prop-types';
import { isEmpty } from 'lodash';
import DocumentApprovalSection from '../../customer-details/readiness-modals/modal-partials/DocumentApprovalSection';
import { productTypes } from '../config';

const MaskComponent = (props) => {
  const {
    docData, customerId, onSelectPoiFile, onMaskComplete,
  } = props;
  const docApprovalProps = {};

  Object.keys(docData).forEach((doc) => {
    const currProps = docData[doc].map((item) => ({
      ...item,
      imgUrl: item.docUrl,
    }));
    if (!isEmpty(currProps)) {
      docApprovalProps[doc] = {
        ...currProps,
      };
    }
  });
  return (
    <>
      {Object.keys(docApprovalProps).map((key) => (
        <div className="doc-container">
          <h4 className="upload-header">{productTypes[key]}</h4>
          <DocumentApprovalSection
            onChange={onSelectPoiFile}
            onSubmit={(index, blobObject) => onMaskComplete(index, blobObject, productTypes[key])}
            error={null}
            docData={docApprovalProps[key]}
            isActionEnabled
            isDisabled={false}
            customerId={customerId}
            fetchReadinessDetails={() => {}}
            showFrontBack
            showRevertButton={false}
            showBlurOption
            showCropOption={false}
            showRotateOption={false}
            showEditImgForBackside
            showFrontBackSwitch={false}
          />
        </div>
      ))}
    </>
  );
};

MaskComponent.propTypes = {
  docData: PropTypes.object.isRequired,
  customerId: PropTypes.string.isRequired,
  onSelectPoiFile: PropTypes.func.isRequired,
  onMaskComplete: PropTypes.func.isRequired,
};

export default MaskComponent;
