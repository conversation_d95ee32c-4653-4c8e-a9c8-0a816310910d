const productTypes = {
  core: 'Core',
  equity: 'Equity',
  nps: 'NPS',
};

const getProductInfo = (product) => {
  const productObj = { isCore: 0 };
  switch (product) {
    case productTypes.equity:
      productObj.product = 'EQUITY';
      productObj.subProduct = 'ALL';
      break;
    case productTypes.nps:
      productObj.product = 'NPS';
      productObj.subProduct = 'NPS';
      break;
    default:
      productObj.isCore = 1;
      productObj.product = '';
      productObj.subProduct = '';
  }
  return productObj;
};

export { productTypes, getProductInfo };
