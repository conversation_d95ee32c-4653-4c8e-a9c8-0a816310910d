import ApiService from '../../../../services/ApiService';
import { ENDPOINTS } from '../../../../constants/api-constants';

const getDocuments = (params) => {
  const reqObj = ENDPOINTS.aadharMasking.getDocuments;
  return ApiService.makeRequest(reqObj, params);
};

const uploadCoreMask = (params) => {
  const reqObj = ENDPOINTS.aadharMasking.uploadCoreMask;
  const headers = { 'Content-Type': 'multipart/form-data' };
  return ApiService.makeRequest(reqObj, params, null, headers);
};

export {
  getDocuments,
  uploadCoreMask,
};
