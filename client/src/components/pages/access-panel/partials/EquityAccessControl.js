import React from 'react';
import PropTypes from 'prop-types';
import classNames from 'classnames';

import { PtmButton, PtmInput, PtmToast } from '@paytm-money/frontend-common-v2';
import { STRINGS } from '../config';
import { isMobileNumbersFormValid } from '../utils';

const EquityAccessControl = (props) => {
  const styles = {
    container: 'equity-access-panel__container',
    form: 'equity-access-panel__form',
    formInputWrapper: 'equity-access-panel__form-input-wrapper',
    formInput: 'equity-access-panel__form-input',
    formInputPrefix: 'equity-access-panel__form-input-prefix',
  };

  const getPhoneNumberInput = () => {
    const phoneInputsArray = [];
    const forLoopLength = props.showResultScreen ? props.mobileNumbers.length : 10;
    for (let index = 0; index < forLoopLength; index += 1) {
      const currMobileObj = props.mobileNumbers[index];
      phoneInputsArray.push(
        <PtmInput
          id={`phoneNumber-${index}`}
          value={currMobileObj.mobileNumber}
          onChange={(e) => props.handleInputChange(index, e.target.value)}
          roundedCorners
          disabled={props.showResultScreen}
          darkBorder
        >
          <p className={styles.formInputPrefix}>+91 </p>
          {(currMobileObj.isUsed || props.showResultScreen) && (
            <i
              className={classNames('fa', {
                'fa-check-circle': currMobileObj.status,
                'fa-times-circle': !currMobileObj.status,
              })}
              style={{ color: currMobileObj.status ? 'green' : 'red', fontSize: '20px' }}
            />
          )}
        </PtmInput>,
      );
    }
    return phoneInputsArray;
  };

  return (
    <div>
      <div className="block block-transparent" style={{ background: 'transparent', margin: '0px 0px 10px 14px' }}>
        <div className="block-content block-content-full">
          <h1 className="h2 text-white push-5-t">{STRINGS.HEADING}</h1>
        </div>
      </div>
      <PtmToast isVisible={Boolean(props.error)} message={props.error} />
      <div className={styles.container}>
        <h3>
          {props.showResultScreen
            ? STRINGS.SUB_HEADING_RESULT_SCREEN
            : STRINGS.SUB_HEADING_MAIN_SCREEN}
        </h3>
        {getPhoneNumberInput()}
        <div>
          {props.showResultScreen ? (
            <PtmButton
              appearance="primary"
              handlePress={props.resetToMainScreen}
            >
              New Request
            </PtmButton>
          ) : (
            <PtmButton
              appearance="primary"
              handlePress={props.handleGiveAccessRequest}
              disabled={!isMobileNumbersFormValid(props.mobileNumbers)}
            >
              Give Access
            </PtmButton>
          )}
        </div>
      </div>
    </div>
  );
};

EquityAccessControl.propTypes = {
  error: PropTypes.string.isRequired,
  mobileNumbers: PropTypes.array.isRequired,
  showResultScreen: PropTypes.bool.isRequired,
  handleInputChange: PropTypes.func.isRequired,
  resetToMainScreen: PropTypes.func.isRequired,
  handleGiveAccessRequest: PropTypes.func.isRequired,
};

export default EquityAccessControl;
