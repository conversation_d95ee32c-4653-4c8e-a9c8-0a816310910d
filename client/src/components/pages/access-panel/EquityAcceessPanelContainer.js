import React from 'react';

import ApiService from '../../../services/ApiService';
import { ENDPOINTS } from '../../../constants/api-constants';
import './equity-access-panel.scss';
import Loader from '../../common/Loader';
import { STRINGS } from './config';
import { getRequestPayload, mobileNumberValidation } from './utils';
import EquityAccessControl from './partials/EquityAccessControl';

const initialStateMobileNumbers = Array(10).fill({ mobileNumber: '', status: false, isUsed: false });

const initialState = {
  isLoading: false,
  mobileNumbers: initialStateMobileNumbers,
  showResultScreen: false,
  error: null,
};

const reducer = (prevState, newState) => ({ ...prevState, ...newState });

const EquityAccessPanelContainer = () => {
  const [state, setState] = React.useReducer(reducer, initialState);

  const handleGiveAccessRequest = () => {
    setState({ isLoading: true });
    ApiService
      .makeRequest(
        ENDPOINTS.accessPanel.updateEquityEligibleUsers,
        getRequestPayload(state.mobileNumbers),
      ).then((response) => {
        setState({ mobileNumbers: response, isLoading: false, showResultScreen: true });
      }).catch(() => setState({ error: STRINGS.API_CALL_FAIL, isLoading: false }));
  };

  const resetToMainScreen = () => {
    setState({ mobileNumbers: initialStateMobileNumbers, showResultScreen: false });
  };

  const handleInputChange = (index, mobileNumber) => {
    const payload = [...state.mobileNumbers];
    payload[index] = {
      mobileNumber,
      status: mobileNumberValidation(mobileNumber),
      isUsed: Boolean(mobileNumber),
    };
    setState({ mobileNumbers: payload });
  };

  if (state.isLoading) {
    return (<Loader.ContentLoader />);
  }

  return (
    <EquityAccessControl
      error={state.error}
      showResultScreen={state.showResultScreen}
      mobileNumbers={state.mobileNumbers}
      handleInputChange={handleInputChange}
      resetToMainScreen={resetToMainScreen}
      handleGiveAccessRequest={handleGiveAccessRequest}
    />
  );
};
export default EquityAccessPanelContainer;
