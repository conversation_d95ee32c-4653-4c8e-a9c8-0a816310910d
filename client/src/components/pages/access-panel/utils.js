const getRequestPayload = (inputNumbers) => {
  const mobileNumbers = [];
  inputNumbers.forEach((mobileObj) => {
    if (mobileObj.mobileNumber) {
      mobileNumbers.push(mobileObj.mobileNumber);
    }
  });
  return { mobileNumbers };
};

const mobileNumberValidation = (mobileNumber) => /^[1-9]{1}\d{9}$/.test(mobileNumber);

const isMobileNumbersFormValid = (inputNumbers) => {
  let isFormValid = true;
  let isFormUsed = false;
  inputNumbers
    .forEach((inputNumber) => {
      if (inputNumber.isUsed) {
        isFormUsed = true;
        isFormValid = isFormValid && inputNumber.status;
      }
    });
  return isFormValid && isFormUsed;
};


export { getRequestPayload, mobileNumberValidation, isMobileNumbersFormValid };
