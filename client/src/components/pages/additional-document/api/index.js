import { ENDPOINTS } from '../../../../constants/api-constants';
import ApiService from '../../../../services/ApiService';

const getAdditionalDocs = (params = {}) => {
  const reqObj = ENDPOINTS.additionalDocs.getAdditionalDocs;
  return ApiService.makeRequest(reqObj, params);
};

const setDocStatus = (params) => {
  const reqObj = ENDPOINTS.additionalDocs.setDocStatus;
  return ApiService.makeRequest(reqObj, params);
};

const getDocFile = (params) => {
  const reqObj = ENDPOINTS.additionalDocs.getDocFile;
  return ApiService.makeRequest(reqObj, params);
};

export {
  getAdditionalDocs,
  setDocStatus,
  getDocFile,
};
