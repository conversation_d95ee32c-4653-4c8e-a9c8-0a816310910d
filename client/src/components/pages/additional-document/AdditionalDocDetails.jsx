import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  PtmBootstrapTable, PtmToast, PtmModal, PtmButton, PtmInput, PtmCheckbox,
} from '@paytm-money/frontend-common-v2';
import OptionSelection from './OptionRejectFields';

import Loader from '../../common/Loader';
import SearchBar from '../../common/search-bar/index';
import Button from '../../common/ActionButton';
import { getAdditionalDocs, setDocStatus, getDocFile } from './api/index';
import { createAndDownloadBlobFile } from './util';
import {
  additionaDocTableConfig, modalStyle, COMMON_KEYS, dropdownOptionsStatus,
} from './config';
import './styles.scss';
import '../../../styles/helper.scss';
import SelectBox from '../../common/SelectBox';
import PdfViewer from '../../common/pdf-viewer';

const AdditionalDocDetails = ({ agentId }) => {
  const [data, setData] = useState([]);
  const [userId, setUserId] = useState('');
  const [pagination, setPagination] = useState({});
  const [curPage, setCurPage] = useState(0);
  const [loading, setLoader] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState({});
  const [isError, setError] = useState(false);
  const [isVisible, setVisible] = useState(false);
  const [errorMsg, setErrorMsg] = useState('');
  const [isParkFilter, setParkFilter] = useState(false);
  const [documentTypes, setDocumentTypes] = useState([]);
  const [filter, setFilter] = useState();
  const changeState = () => {
    setVisible(false);
  };

  const fetchFilteredTickets = (filterObj, parkFilter) => {
    setLoader(true);
    const params = {
      ...(filter && filter.status ? { status: [filter.status.value] } : {}),
      ...(filter && filter.docType ? { docType: filter.docType.value } : {}),
      isParkFilter: parkFilter !== undefined ? parkFilter : isParkFilter,
      userId: userId.trim(),
      page: curPage,
    };
    if (filterObj) {
      if (filterObj.status === null) delete params.status;
      if (filterObj.docType === null) delete params.docType;
      if (filterObj.userId === null) delete params.userId;
    }
    getAdditionalDocs(params).then((response) => {
      setLoader(false);
      setData((response || {}).additionalDocumentsList || []);
      setDocumentTypes((response || {}).allDocList || []);
      setPagination((response || {}).paginationParameters);
    }).catch(({ error }) => {
      setError(true);
      setErrorMsg(error);
      setLoader(false);
      setVisible(true);
    });
  };

  useEffect(() => {
    fetchFilteredTickets();
  }, [curPage]);

  const [isOpen, setOpen] = useState(false);

  const toggleModal = () => {
    setOpen(!isOpen);
  };

  const onUserSearch = (userId) => {
    setUserId(userId);
  };
  const onSave = (id, selectedField) => {
    const status = selectedField.selectedOption === 1 ? COMMON_KEYS.ACCEPTED : COMMON_KEYS.REJECTED;
    const rejectionReason = selectedField.selectedDisplayReasonText;
    const params = {
      status,
      rejectionReason,
      docId: id,
    };
    toggleModal();
    setDocStatus(params)
      .then(() => {
        setError(false);
        setErrorMsg(COMMON_KEYS.SUCCESS_MSG);
        setVisible(true);
        setOpen(false);
        fetchFilteredTickets();
      })
      .catch(({ error }) => {
        setVisible(true);
        setError(true);
        setErrorMsg(error);
      });
  };
  const getImgText = (docCode) => {
    if (docCode === 'FA') {
      return COMMON_KEYS.FRONT_IMG;
    } if (docCode === 'BA') {
      return COMMON_KEYS.BACK_IMG;
    }
    return 'IMG';
  };

  const downloadDoc = (url, userId, documentType, side) => {
    const param = {
      url,
    };
    getDocFile(param).then((file) => createAndDownloadBlobFile(file, `${userId}_${documentType.code}_${side}`, (selectedRecord || {}).displayId.includes('getpdf') ? 'pdf' : 'jpg'))
      .catch(({ error }) => {
        setVisible(true);
        setError(true);
        setErrorMsg(error);
      });
  };
  const renderDownloadData = (id) => {
    const selectedItem = (data || []).filter((item) => item.docId === id)[0] || {};
    return (
      <div className="action-container">
        <div className="front-document">
          <Button.IconButton
            title="FA search"
            buttonStyle={'add-doc-download'}
            iconClass="fa fa-download text-bold"
            onClickAction={() => {
              downloadDoc(selectedItem.displayId, selectedItem.userId, selectedItem.documentType, 'FA');
            }}
          />
          <span>{selectedItem.backDisplayId ? COMMON_KEYS.FA_DOC : `${(selectedItem.documentType || {}).displayName} Doc`}</span>
        </div>
        {selectedItem.backDisplayId ? (
          <div className="back-document">
            <Button.IconButton
              title="FA search"
              buttonStyle={'add-doc-download'}
              iconClass="fa fa-download text-bold"
              onClickAction={() => {
                downloadDoc(selectedItem.backDisplayId, selectedItem.userId, selectedItem.documentType, 'BA');
              }}
            />
            <span>{COMMON_KEYS.BA_DOC }</span>
          </div>
        ) : null}
        {selectedItem.supportingDocumentDisplayId ? (
          <div className="back-document">
            <Button.IconButton
              title="FA search"
              buttonStyle={'add-doc-download'}
              iconClass="fa fa-download text-bold"
              onClickAction={() => {
                downloadDoc(selectedItem.supportingDocumentDisplayId, selectedItem.userId, selectedItem.documentType, 'BA');
              }}
            />
            <span>{COMMON_KEYS.SUPPOTING_DOC_IMG }</span>
          </div>
        ) : null}
      </div>
    );
  };

  const imageContainer = (
    <div className="image-container">
      <div className="front-img">
        <span className="h3 push-30 visible-md visible-lg font-w600 text-primary-darker">{getImgText((selectedRecord || {}).documentCode)}</span>
        <img
          src={(selectedRecord || {}).displayId}
          alt="frontImg"
          crossOrigin="use-credentials"
          className="editable-img"
        />
      </div>
      {(selectedRecord || {}).backDisplayId ? (
        <div className="back-img">
          <span className="h3 push-30 visible-md visible-lg font-w600 text-primary-darker">{COMMON_KEYS.BACK_IMG}</span>
          <img
            src={(selectedRecord || {}).backDisplayId}
            alt="backImg"
            crossOrigin="use-credentials"
            className="editable-img"
          />
        </div>
      ) : null}
      {(selectedRecord || {}).supportingDocumentDisplayId ? (
        <div className="back-img">
          <span className="h3 push-30 visible-md visible-lg font-w600 text-primary-darker">{COMMON_KEYS.SUPPOTING_DOC_IMG}</span>
          <img
            src={(selectedRecord || {}).supportingDocumentDisplayId}
            alt="backImg"
            className="editable-img"
          />
        </div>
      ) : null}
    </div>
  );

  const getDocContainer = () => {
    if (!(selectedRecord || {}).displayId) return null;
    if ((selectedRecord || {}).displayId.includes('getpdf')) {
      return (
        <PdfViewer
          url={(selectedRecord || {}).displayId}
          width={550}
          withCredentials
        />
      );
    } return imageContainer;
  };

  const openDocInfo = (id) => {
    const selectedItem = (data || []).filter((item) => item.docId === id);
    setSelectedRecord(selectedItem[0] || {});
    toggleModal();
  };
  const onChangePageNumber = (pageNo) => {
    setCurPage(parseInt(pageNo) !== NaN ? parseInt(pageNo) - 1 : 0);
  };
  const configProps = {
    openDocInfo,
    renderDownloadData,
  };

  const clearFilter = () => {
    setFilter({
      status: null,
      docType: null,
    });
    setUserId('');
    setParkFilter(false);
    fetchFilteredTickets({
      status: null,
      docType: null,
      userId: null,
    }, false);
  };

  const handleSelectFilters = (value, name) => {
    setFilter({
      ...filter,
      [name]: value,
    });
    if (!value) {
      fetchFilteredTickets({
        ...filter,
        [name]: value,
      });
    }
  };

  const handleSearchClearFn = () => {
    setUserId('');
    fetchFilteredTickets({
      ...filter,
      userId: null,
    });
  };

  const isActionEnabled = (selectedRecord || {}).docStatus === COMMON_KEYS.SUBMITTED;
  return (
    <div className="additional-doc-details content">
      <PtmToast
        containerClass={isError ? 'error' : 'success'}
        message={errorMsg}
        changeState={changeState}
        isVisible={isVisible}
      />
      <h1 className="h2 text-white push-10-t push-20">{COMMON_KEYS.HEADING}</h1>
      <div className="user-search">
        <div className="filter-container">
          <div className={'filter-select'}>
            <p>Doc Type</p>
            <SelectBox
              className="select_box"
              options={documentTypes}
              onChange={(value) => handleSelectFilters(value, 'docType')}
              value={filter && filter.docType}
            />
          </div>
          <div className={'filter-select'}>
            <p>Status</p>
            <SelectBox
              className="select_box"
              options={dropdownOptionsStatus}
              onChange={(value) => handleSelectFilters(value, 'status')}
              id="status"
              value={filter && filter.status}
            />
          </div>
          <PtmInput
            id="action-input"
            value={userId}
            onChange={(e) => onUserSearch(e.target.value)}
            placeholder={COMMON_KEYS.INP_PLACE_HOLDER}
            roundedCorners
            darkBorder
          >
            <i className="fa fa-2x fa-times" role="presentation" onClick={handleSearchClearFn} />
          </PtmInput>
          <div className={'parked-filter'}>
            <PtmCheckbox
              label={'Parked Tickets'}
              checked={isParkFilter}
              onChange={() => {
                setParkFilter(!isParkFilter);
              }}
            />
          </div>
          <PtmButton
            className={'filter-button'}
            appearance={'primary'}
            handlePress={fetchFilteredTickets}
          >
            Filter Tickets

          </PtmButton>
          <PtmButton
            className={'clear-filter-button'}
            appearance={'secondary'}
            handlePress={clearFilter}
          >
            Clear Filter
          </PtmButton>
        </div>
      </div>
      { loading ? (<Loader.SectionLoader />) : (
        <div>
          <PtmBootstrapTable
            headerConfigArr={additionaDocTableConfig(configProps)}
            data={data || []}
            keyField="userId"
            totalCount={pagination.totalElements || 0}
            onChangePageNumber={onChangePageNumber}
            pageNumber={parseInt(pagination.pageNumber) + 1 || 0}
            sizePerPage={pagination.pageSize || 0}
          />
          {// below is pml modal
          }
          <PtmModal
            isOpen={isOpen}
            onClose={toggleModal}
            style={modalStyle}
          >
            <div className="modal-content">
              {getDocContainer()}
              {(selectedRecord || {}).docStatus === COMMON_KEYS.SUBMITTED ? (
                <div className="docnumber-content">
                  {
                    // below code contains addition of reject reasons component
                  }
                  <OptionSelection
                    rejectReasonList={selectedRecord.rejectReasonList}
                    isActionEnabled={isActionEnabled}
                    docId={(selectedRecord || {}).docId}
                    onSave={onSave}
                  />
                </div>
              ) : null}
            </div>
          </PtmModal>
        </div>
      )}
    </div>
  );
};

AdditionalDocDetails.propTypes = {
  agentId: PropTypes.string.isRequired,
};

export default AdditionalDocDetails;
