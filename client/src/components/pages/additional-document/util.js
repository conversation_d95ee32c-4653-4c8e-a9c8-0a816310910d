import moment from 'moment';
import map from 'lodash/map';

const createAndDownloadBlobFile = (blob, filename, extension = 'pdf') => {
  const fileName = `${filename}.${extension}`;
  if (navigator.msSaveBlob) {
    navigator.msSaveBlob(blob, fileName);
  } else {
    const link = document.createElement('a');
    // Browsers that support HTML5 download attribute
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', fileName);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }
};

const computeTimestampInSec = (timestamp) => {
  return timestamp.toString().length > 10 ? timestamp / 1000 : timestamp;
};
const convertTimeStampToDateFormat = (timestamp, format = 'DD-MM-YYYY') => {
  if (!timestamp) {
    return null;
  }
  const timeStampInSec = computeTimestampInSec(timestamp);
  return moment.unix(timeStampInSec).format(format);
};

const formatPrimaryCategoryReason = (categoryList) => (
  map(categoryList, (category) => ({ label: category, value: category }))
);

const APPROVAL_MODAL_OPTIONS = {
  APPROVE: 1,
  REJECT: 2,
  CHANGE_INFO_REQUEST: 3,
};

const formatRejectReasons = (optionsList) => (
  optionsList.map((option) => option && ({ label: option.agentReason, value: option.id }))
);

export {
  convertTimeStampToDateFormat,
  createAndDownloadBlobFile,
  formatPrimaryCategoryReason,
  formatRejectReasons,
  APPROVAL_MODAL_OPTIONS
};
