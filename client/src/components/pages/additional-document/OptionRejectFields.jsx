import React, { Component } from 'react';
import find from 'lodash/find';
import { PtmButton } from '@paytm-money/frontend-common-v2';
import PropTypes from 'prop-types';
import Select from 'react-select';
import autobind from 'autobind-decorator';
import isEmpty from 'lodash/isEmpty';
import clasnames from 'classnames';

import { APPROVAL_MODAL_OPTIONS, formatRejectReasons, formatPrimaryCategoryReason } from './util';

function getInitialState() {
  return {
    selectedOption: null,
    isSaveDisabled: true,
    showRejectReason: false,
    selectedReason: null,
    selectedPrimaryCategory: null,
    approveBtnChecked: false,
    rejectBtnChecked: false,
  };
}
@autobind
class OptionSelection extends Component {
  constructor(props) {
    super(props);
    this.state = getInitialState();
    const primaryCategoryRejectionList = Object.keys(props.rejectReasonList);
    this.formattedPrimaryCategoryOptions = formatPrimaryCategoryReason(
      primaryCategoryRejectionList,
    );
  }

  componentWillReceiveProps(nextProps) {
    const { fieldName, disableApproveOptn, disableRejectOptn } = this.props;
    if (fieldName && nextProps.fieldName) {
      this.restoreState();
      const primaryCategoryRejectionList = Object.keys(nextProps.rejectReasonList);
      this.formattedPrimaryCategoryOptions = formatPrimaryCategoryReason(
        primaryCategoryRejectionList,
      );
    }

    if (!disableApproveOptn && nextProps.disableApproveOptn === true) {
      this.setState((state) => {
        if (state.approveBtnChecked && !state.isSaveDisabled) {
          return {
            approveBtnChecked: false,
            isSaveDisabled: true,
            selectedPrimaryCategory: null,
          };
        }
        return {
          approveBtnChecked: false,
          selectedPrimaryCategory: null,
        };
      });
    }

    if (!disableRejectOptn && nextProps.disableRejectOptn === true) {
      this.setState((state) => {
        if (state.rejectBtnChecked && !state.isSaveDisabled) {
          return {
            rejectBtnChecked: false,
            isSaveDisabled: true,
          };
        }
        return {
          rejectBtnChecked: false,
        };
      });
    }
  }

  onChangeSelectionOption = (selectedOptnId) => {
    if (selectedOptnId === APPROVAL_MODAL_OPTIONS.APPROVE) {
      this.setState({
        approveBtnChecked: true,
        rejectBtnChecked: false,
      });
    }

    if (selectedOptnId === APPROVAL_MODAL_OPTIONS.REJECT) {
      this.setState({
        rejectBtnChecked: true,
        approveBtnChecked: false,
      });
    }

    this.setState({
      selectedOption: selectedOptnId,
      isSaveDisabled: selectedOptnId === APPROVAL_MODAL_OPTIONS.REJECT,
      showRejectReason: selectedOptnId === APPROVAL_MODAL_OPTIONS.REJECT,
      // resetting the selected reason
      selectedPrimaryCategory: null,
      selectedReason: null,
    });
  }

  onSaveAction() {
    const { selectedOption, selectedReason, selectedPrimaryCategory } = this.state;
    let selectedDisplayReasonText;
    if (selectedOption === APPROVAL_MODAL_OPTIONS.REJECT) {
      const rejectReasonList = this.props.rejectReasonList[selectedPrimaryCategory];
      selectedDisplayReasonText = find(rejectReasonList,
        { id: selectedReason.value }).displayReason;
    }
    this.props.onSave(this.props.docId, { selectedOption, selectedReason, selectedDisplayReasonText });
  }

  getRejectionOption() {
    const { selectedPrimaryCategory } = this.state;
    const { rejectReasonList } = this.props;
    const optionList = rejectReasonList[selectedPrimaryCategory];
    return formatRejectReasons(optionList);
  }

  handlePrimaryCategoryChange = (valObj) => {
    if (!valObj || isEmpty(valObj)) {
      this.setState({ selectedPrimaryCategory: null, isSaveDisabled: true, selectedReason: null });
    }

    this.setState({
      selectedPrimaryCategory: valObj.value,
      selectedReason: null,
      isSaveDisabled: true,
    });
  };

  handleSelectChange(val) {
    if (!val && isEmpty(val)) {
      this.setState({ isSaveDisabled: true, selectedReason: null });
    } else {
      this.setState({ isSaveDisabled: false, selectedReason: val });
    }
  }

  restoreState() {
    const initialState = getInitialState();
    this.setState(initialState);
  }

  render() {
    const {
      parentContainer,
      isActionEnabled,
      disableApproveOptn,
      disableRejectOptn,
    } = this.props;

    const approveBtnContainerClass = clasnames('css-input css-radio css-radio-lg css-radio-success push-10-r h4', {
      'css-input-disabled': disableApproveOptn,
    });
    const rejectBtnContainerClass = clasnames('css-input css-radio css-radio-lg css-radio-danger push-50-l h4', {
      'css-inpu-diabled': disableRejectOptn,
    });

    const {
      approveBtnChecked,
      rejectBtnChecked,
      isSaveDisabled,
      showRejectReason,
      selectedPrimaryCategory,
      selectedReason,
    } = this.state;
    if (!isActionEnabled) {
      return null;
    }

    return (
      <div className={parentContainer}>
        <div className="col-md-12 push-20-t pull-left">
          <label className={approveBtnContainerClass} htmlFor="approve">
            <input
              id="approve"
              type="radio"
              name="radio-group2"
              checked={approveBtnChecked}
              onChange={() => this.onChangeSelectionOption(APPROVAL_MODAL_OPTIONS.APPROVE)}
              disabled={disableApproveOptn}
            />
            <span />
            Approve
          </label>

          <label className={rejectBtnContainerClass} htmlFor="reject">
            <input
              id="reject"
              type="radio"
              name="radio-group2"
              checked={rejectBtnChecked}
              onChange={() => this.onChangeSelectionOption(APPROVAL_MODAL_OPTIONS.REJECT)}
              disabled={disableRejectOptn}
            />
            <span />
            Reject
          </label>
          <PtmButton
            className="col-md-4 pull-right"
            disabled={isSaveDisabled}
            handlePress={this.onSaveAction}
            appearance="primaryDark"
          >
            Save Changes
          </PtmButton>
        </div>
        {
          showRejectReason
            ? (
              <div className="col-md-12 pull-left push-50">
                <div className="push-20-t push-50">
                  <Select
                    isClearable
                    onChange={this.handlePrimaryCategoryChange}
                    placeholder="Select Primary Reject Reasons"
                    options={this.formattedPrimaryCategoryOptions}
                  />
                </div>
                {
                  selectedPrimaryCategory
                  && (
                  <div className="push-20-t">
                    <Select
                      isClearable
                      value={selectedReason}
                      onChange={this.handleSelectChange}
                      placeholder="Select Reject Reasons"
                      options={this.getRejectionOption()}
                    />
                  </div>
                  )
                }
              </div>
            ) : null
        }
      </div>
    );
  }
}

OptionSelection.propTypes = {
  onSave: PropTypes.func.isRequired,
  rejectReasonList: PropTypes.object.isRequired,
  fieldName: PropTypes.string,
  isActionEnabled: PropTypes.bool.isRequired,
  parentContainer: PropTypes.string,
  disableApproveOptn: PropTypes.bool,
  disableRejectOptn: PropTypes.bool,
  docId: PropTypes.string.isRequired,
};

OptionSelection.defaultProps = {
  fieldName: '',
  parentContainer: 'row push-20-t border-t push-20 remove-margin-l remove-margin-r',
  disableApproveOptn: false,
  disableRejectOptn: false,
};

export default OptionSelection;
