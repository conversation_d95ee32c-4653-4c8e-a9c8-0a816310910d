@import '../../../styles/variables';

.additional-doc-details {
  background-color: $white;

  h1 {
    color: $grey-base;
  }

  table td {
    overflow: visible;
  }

  .user-search {
    display: flex;
    justify-content: flex-end;
    margin: 10px 0;
    gap: 2rem;
  }

  .filter-button {
    height: 4rem;
    min-width: 15rem;
  }

  .action-container {
    display: flex;
    justify-content: center;
  }

  .action-button {
    margin: 0 10px;
  }
}

.front-document,
.back-document {
  display: flex;
  flex-direction: column;
  margin: 0 10px;
}

.add-doc-download {
  background: transparent;
  border: 0;
  margin-bottom: 5px;
}

.file-container {
  align-items: center;
  display: flex;
  font-size: 18px;
  height: 100%;
  justify-content: center;
}

.image-container {
  display: flex;
  justify-content: space-between;
  padding: 0 30px;

  .front-img,
  .back-img {
    max-width: 40%;
  }

  img {
    width: 100%;
  }
}

.modal-content {
  background: transparent;
  margin: 0 30px;

  .save-button {
    margin-left: 20px;
  }

  .docnumber-content {
    display: flex;
    max-width: 40%;
    padding: 30px;
  }
}

.filter-container{
  display: flex;
  gap: 3rem;
  width: 100%;
  align-items: end;
}

.filter-select {
  width: 100%;
  display: flex;
  flex-direction: column;

  p {
    margin-bottom: 0px;
  }
}

.input-group {
  width: 100%;
}

.parked-filter {
  width: 100%;
}

.clear-filter-button {
  height: 4rem;
  min-width: 15rem;
}