import React from 'react';
import {
  PtmButton,
} from '@paytm-money/frontend-common-v2';
import { convertTimeStampToDateFormat } from './util';

const additionaDocTableConfig = ({
  openDocInfo,
  renderDownloadData,
}) => [
  {
    title: 'User id',
    dataField: 'userId',
    width: '15%',
  },
  {
    title: 'Name',
    dataField: 'userName',
    width: '15%',
  },
  {
    title: 'Date',
    dataField: 'submittedAt',
    width: '15%',
    dataFormat: (dateTimestamp) => convertTimeStampToDateFormat(dateTimestamp, 'DD-MM-YYYY'),
  },
  {
    title: 'Doc Type',
    dataField: 'documentType',
    width: '15%',
    dataFormat: (docType) => (docType || {}).displayName,
  },
  {
    title: 'Product',
    dataField: 'product',
    width: '10%',
  },
  {
    title: 'Parked Ticket',
    dataField: 'parkUnparkStatus',
    width: '10%',
    dataFormat: (cell) => (cell ? 'YES' : 'NO'),
  },
  {
    title: 'View Document',
    dataField: 'docId',
    width: '15%',
    dataFormat: (docId) => (
      <div className="action-container">
        <PtmButton
          appearance={'primary'}
          className={'action-button'}
          handlePress={() => { openDocInfo(docId); }}
        >
          View Doc
        </PtmButton>
      </div>
    ),
  },
  {
    title: 'Action',
    dataField: 'docStatus',
    width: '10%',
  },
  {
    title: 'Download',
    dataField: 'docId',
    width: '25%',
    dataFormat: (docId) => renderDownloadData(docId),
  },
];

const COMMON_KEYS = {
  FRONT_IMG: 'Front Image',
  BACK_IMG: 'Back Image',
  HEADING: 'Additional Doc List',
  SUPPOTING_DOC_IMG: 'Supporting Doc Image',
  SUCCESS_MSG: 'Successfully Updated',
  PAGE_SIZE: 50,
  KN_INFO: 'KRA Verified User. ',
  DOWNLOAD: 'Download ZIP File',
  APPROVE: 'Approve',
  PARKED: 'Parked Tickets',
  UNPARKED: 'All Tickets',
  REJECT: 'Reject',
  DOC_TYPE: 'docType',
  INP_PLACE_HOLDER: 'Search User ID',
  ACCEPTED: 'ACCEPTED',
  REJECTED: 'REJECTED',
  SUBMITTED: 'SUBMITTED',
  FA_DOC: 'FA Doc',
  BA_DOC: 'BA Doc',
};

const modalStyle = {
  content: {
    top: '50%',
    left: '50%',
    right: 'auto',
    bottom: 'auto',
    marginRight: '-50%',
    transform: 'translate(-50%, -50%)',
    zIndex: 1090,
    width: '80%',
    height: '80%',
  },
};

const dropdownOptionsStatus = [
  { label: 'SUBMITTED', value: 'SUBMITTED' },
  { label: 'ACCEPTED', value: 'ACCEPTED' },
  { label: 'REJECTED', value: 'REJECTED' },
];

export {
  additionaDocTableConfig,
  COMMON_KEYS,
  modalStyle,
  dropdownOptionsStatus,
};
