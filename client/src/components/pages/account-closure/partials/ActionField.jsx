import React, { useState } from 'react';
import { PtmButton, PtmInput } from '@paytm-money/frontend-common-v2';
import PropTypes from 'prop-types';

const getComment = (feedback) => {
  try {
    const parts = JSON.parse(feedback);
    return parts[0].comment;
  } catch (error) {
    return feedback;
  }
};

const ActionField = ({
  currTab,
  actionColumnFields,
  handleStatusUpdate,
  data,
  possibleStatusList: possibleStatuses,
}) => {
  const {
    inputPlaceHolder,
    firstButtonLabel,
    secondButtonLabel,
    firstButtonStatus,
    secondButtonStatus,
  } = actionColumnFields[currTab];
  const {
    id, feedback, status, userId,
  } = data;
  const [comment, setComment] = useState(getComment(feedback) || '');

  const isBtnDisabled = (reqStatus, btnStatus) => !possibleStatuses[reqStatus].includes(btnStatus);

  return (
    <div className="action-container">
      <PtmInput
        id="action-input"
        value={comment}
        onChange={(e) => setComment(e.target.value)}
        placeholder={inputPlaceHolder}
        roundedCorners
        containerClassname="action-input"
        disabled={isBtnDisabled(status, firstButtonStatus) || isBtnDisabled(status, secondButtonStatus)}
      />
      <PtmButton
        appearance={'primary'}
        className="action-button"
        disabled={isBtnDisabled(status, firstButtonStatus)}
        onClick={() => handleStatusUpdate(firstButtonStatus, id, comment, userId)}
      >
        {firstButtonLabel}
      </PtmButton>
      <PtmButton
        appearance={'primary'}
        className="action-button push-5-l"
        disabled={isBtnDisabled(status, secondButtonStatus)}
        onClick={() => handleStatusUpdate(secondButtonStatus, id, comment, userId)}
      >
        {secondButtonLabel}
      </PtmButton>
    </div>
  );
};

ActionField.propTypes = {
  currTab: PropTypes.string.isRequired,
  actionColumnFields: PropTypes.object.isRequired,
  handleStatusUpdate: PropTypes.func.isRequired,
  data: PropTypes.object.isRequired,
  possibleStatusList: PropTypes.object.isRequired,
};

export default ActionField;
