import React from 'react';
import PropTypes from 'prop-types';
import { PtmNavTabs } from '@paytm-money/frontend-common-v2';
import { navList } from '../config';

const HeaderTabs = ({ setCurrTab, tabsList = navList }) => {
  const extFunc = (index, title) => {
    setCurrTab(title);
  };

  return (
    <PtmNavTabs
      type="primary"
      containerExtStyle="sub-nav-tabs"
      textExtStyle="text-style h4 push-10-l"
      activeIndex={0}
      extFunc={extFunc}
      navList={tabsList}
    />
  );
};

HeaderTabs.propTypes = {
  setCurrTab: PropTypes.func.isRequired,
  tabsList: PropTypes.array.isRequired,
};

export default HeaderTabs;
