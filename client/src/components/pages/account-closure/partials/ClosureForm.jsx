import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import Loader from '../../../common/Loader';
import { userDetailsInfo } from '../config';
import PdfViewer from '../../../common/pdf-viewer';
import { ENDPOINTS } from '../../../../constants/api-constants';

const renderInfoBody = (data) => (
  <>
    {userDetailsInfo.map((row, index) => (
      <div className="data-row" key={index}>
        <div className="h5 text-gray-dark">{row.name}</div>
        <div className="h4 text-city-dark push-5-t">{data[row.field] || '-'}</div>
      </div>
    ))}
  </>
);

const ClosureForm = (props) => {
  const {
    currRequest = {}, loading, userData = [], error = '',
  } = props;
  const [pdfUrl, setPdfUrl] = useState(null);
  const urlParts = currRequest.closureFormUrl.split('/');
  const url = urlParts[urlParts.length - 1];
  const params = {
    formId: url,
  };

  const generateBlobUrl = (formUrl) => `${window.location.origin}/api/${ENDPOINTS.accountClosure.getClosureForm.endpoint()}?formId=${encodeURIComponent(formUrl)}`;

  useEffect(() => {
    setPdfUrl(generateBlobUrl(params.formId));
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="closure-form-container">
      <div className="row pull-r-l">
        <div className="col-md-6 col-sm-6">
          <div className="userinfo-header">
            User details
            {error && <div className="h4 text-danger push-10-l">{error}</div>}
          </div>
          {loading ? (
            <>
              <span className="h5">Fetching user details....</span>
              <Loader.SectionLoader />
            </>
          ) : (
            renderInfoBody(userData)
          )}
        </div>
        <div className="col-md-6 border-l">
          <PdfViewer
            url={pdfUrl}
            className="account-opening-form"
            withCredentials={false}
            secondaryCtaText="Download PDF"
          />
        </div>
      </div>
    </div>
  );
};

ClosureForm.propTypes = {
  currRequest: PropTypes.object,
  loading: PropTypes.bool.isRequired,
  userData: PropTypes.object.isRequired,
  error: PropTypes.string,
};

ClosureForm.defaultProps = {
  currRequest: {},
  error: '',
};

export default ClosureForm;
