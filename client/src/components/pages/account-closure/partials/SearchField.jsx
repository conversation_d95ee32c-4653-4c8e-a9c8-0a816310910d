import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { PtmInput, PtmButton } from '@paytm-money/frontend-common-v2';

const SearchField = (props) => {
  const {
    onClick, setUser, user, handleSearchClear = () => {},
  } = props;
  const [searchVal, setSearchVal] = useState(user);
  const handleSearchClearFn = () => {
    setUser('');
    setSearchVal('');
    handleSearchClear();
  };
  const handleSearchClick = async () => {
    setUser(searchVal);
    onClick(searchVal);
  };
  const handleKeyPressSearch = (e) => {
    if (e.key === 'Enter') {
      handleSearchClick();
    }
  };
  return (
    <div className="search-field-container">
      <PtmInput
        id="action-input"
        value={searchVal}
        onChange={(e) => setSearchVal((e.target.value).trim())}
        placeholder={'Enter the user ID to search'}
        roundedCorners
        darkBorder
        onKeyDown={handleKeyPressSearch}
      >
        <i className="fa fa-2x fa-times" role="presentation" onClick={handleSearchClearFn} />
      </PtmInput>
      <PtmButton
        appearance={'primaryDark'}
        className="search-btn"
        handlePress={handleSearchClick}
      >
        <i className="fa fa-search text-light" />
      </PtmButton>
    </div>
  );
};

SearchField.propTypes = {
  onClick: PropTypes.func.isRequired,
  user: PropTypes.string.isRequired,
  setUser: PropTypes.func.isRequired,
  handleSearchClear: PropTypes.func.isRequired,
};

export default SearchField;
