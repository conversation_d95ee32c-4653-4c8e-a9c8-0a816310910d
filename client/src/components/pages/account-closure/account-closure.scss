@import '../../../styles/variables.scss';

.account-closure-container {
    background-color: $white;

    .header-container {
      display: flex;
      flex-direction: row;
    }

    .sub-nav-tabs {
        .text-style {
          color: inherit;
        }
    }

    .action-container {
      display: flex;
      justify-content: flex-start;

      .action-input {
        flex: 1;
      }

      .action-button {
        margin: 0 10px;
      }
    }
}

.closure-form-container {
  .userinfo-header {
    font-size: 18px;
    color: $grey-base;
    margin-bottom: 20px;
    display: flex;
    align-items: baseline;
  }

  .data-row {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid $grey-light-4;
    margin-bottom: 30px;
  }

  .account-opening-form {
    object-fit: contain;
    width: 100%;
    height: 90%;
  }
}

.search-field-container {
  width: 500px;
  margin: 10px 0 10px auto;
  display: flex;
  flex-direction: row;

  .search-btn {
    width: 50px;
    margin-left: 10px;
  }
}

.pagination {
  display: flex;
  font-size: 14px;
  padding-bottom: 20px;

  span {
    margin-top: 5px;
  }
}