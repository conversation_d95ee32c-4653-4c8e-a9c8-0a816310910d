import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { PtmBootstrapTable, PtmToast } from '@paytm-money/frontend-common-v2';
import { navList, statusList, tableConfig } from './config';
import HeaderTabs from './partials/HeaderTabs';
import './account-closure.scss';
import { getClosureRequests, getUserDetails, updateRequestStatus } from './api-util/index';
import Loader from '../../common/Loader';
import SlidingPane from '../../common/SlidingPane';
import ClosureForm from './partials/ClosureForm';
import SearchField from './partials/SearchField';
import Pagination from '../../common/ticket-partials/Pagination';

const AccountClosure = (props) => {
  const [data, setData] = useState([]);
  const [currTab, setCurrTab] = useState(navList[0].title);
  const [currSearchUser, setCurrSearchUser] = useState('');
  const [currModalRequest, setCurrModalRequest] = useState({});
  const [currModalUserData, setCurrModalUserData] = useState({});
  const [isModalOpen, setIsModalOpen] = useState(false);

  const [loading, setloading] = useState(false);
  const [userDetailsLoading, setUserDetailsLoading] = useState(false);

  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [updateSuccessRender, setUpdateSuccessRender] = useState(false);

  const [currPage, setCurrPage] = useState(1);
  const [totalPages, setTotalPages] = useState(2);

  const getParams = (userId = currSearchUser, pageNo = currPage) => (
    {
      statuses: userId ? '' : statusList[currTab].join(','),
      userId,
      offset: pageNo - 1, // offset in API is 0-based indexing
      pageSize: 50,
      product: 'EQUITY',
      subProduct: 'ALL',
    }
  );

  const handleStatusUpdate = (status, closureRequestId, comment, userId) => {
    const params = {
      requestId: closureRequestId,
      userId,
      status,
      comment,
      product: 'EQUITY',
      subProduct: 'ALL',
      tab: currTab,
    };
    updateRequestStatus(params).then(() => {
      setIsError(false);
      setToastMessage('Request has been successfully updated');
      setShowToast(true);
      setTimeout(() => {
        setUpdateSuccessRender(!updateSuccessRender);
      }, 2000);
    }).catch((error) => {
      setIsError(true);
      setToastMessage(error.error || 'Something went wrong');
      setShowToast(true);
    });
  };

  const fetchClosureRequests = (userId = currSearchUser, pageNo = currPage) => {
    setCurrSearchUser(userId);
    setloading(true);

    getClosureRequests(getParams(userId, pageNo)).then((res) => {
      // If no more data is sent from the server, then we assign totalPages to
      // currentPageNum, so that we don't get 'next' icon
      if (!res.data.length) {
        setTotalPages(pageNo);
      } else {
        setTotalPages(pageNo + 1);
      }

      // The below step is done because, for searching we have to send statuses as an
      // empty string, so we need to filter only the status under current tab
      const currStageReqs = res.data.filter((item) => statusList[currTab].includes(item.status));
      setData(currStageReqs);
    }).catch((error) => {
      setIsError(true);
      setToastMessage(error.error || 'Something went wrong');
      setShowToast(true);
    }).finally(() => {
      setloading(false);
    });
  };

  const fetchUserDetails = (userId) => {
    const params = {
      userId,
    };
    setUserDetailsLoading(true);
    getUserDetails(params)
      .then((response) => {
        setErrorMessage('');
        setCurrModalUserData(response);
      })
      .catch((error) => {
        setErrorMessage(error.error || 'Something went wrong');
        setCurrModalUserData({});
      }).finally(() => {
        setUserDetailsLoading(false);
      });
  };

  const actionFieldConfigs = () => (
    {
      currTab,
      openModal: (requestData) => {
        setErrorMessage('');
        setCurrModalRequest(requestData);
        setIsModalOpen(true);
        fetchUserDetails(requestData.userId);
      },
      handleStatusUpdate,
    }
  );

  const handlePageChange = (newPageNum) => {
    setCurrPage(newPageNum);
    fetchClosureRequests(currSearchUser, newPageNum);
  };

  const handleSearchClearFn = () => {
    fetchClosureRequests('');
  };

  useEffect(() => {
    // Pages numbers are set to avoid pagination sync between CS and OPS during tab switch
    setCurrPage(1);
    setTotalPages(2);

    fetchClosureRequests(currSearchUser, 1);
  }, [currTab, updateSuccessRender]);

  return (
    <div className="account-closure-container content">
      <PtmToast
        isVisible={showToast}
        message={toastMessage}
        containerClass={!isError ? 'success' : 'error'}
        changeState={() => setShowToast(false)}
      />
      <h1 className="h2 push-10-t push-20">Account Closure</h1>
      <div className="header-container">
        <HeaderTabs setCurrTab={setCurrTab} />
        <SearchField
          onClick={fetchClosureRequests}
          user={currSearchUser}
          setUser={setCurrSearchUser}
          handleSearchClear={handleSearchClearFn}
        />
      </div>
      {loading ? <Loader.SectionLoader />
        : (
          <>
            <PtmBootstrapTable
              keyField="closureRequestId"
              headerConfigArr={tableConfig(actionFieldConfigs())}
              data={data}
              pagination={false}
              className="push-10-t"
            />
            <Pagination
              className="pagination"
              currentPage={currPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
              displayNumbers={false}
              paginationSize={50}
            />
            <SlidingPane
              isOpen={isModalOpen}
              onRequestClose={() => setIsModalOpen(false)}
              title={<span style={{ fontWeight: '700', fontSize: '20px' }}>Closure Form</span>}
              width="75%"
            >
              <ClosureForm
                currRequest={currModalRequest}
                loading={userDetailsLoading}
                userData={currModalUserData}
                error={errorMessage}
              />
            </SlidingPane>
          </>
        )}
    </div>
  );
};

AccountClosure.propTypes = {
  agentId: PropTypes.number.isRequired,
  agentEmail: PropTypes.string.isRequired,
};

export default AccountClosure;
