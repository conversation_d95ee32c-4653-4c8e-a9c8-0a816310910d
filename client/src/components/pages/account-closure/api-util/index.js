import ApiService from '../../../../services/ApiService';
import { ENDPOINTS } from '../../../../constants/api-constants';

const getClosureRequests = (params) => {
  const reqObj = ENDPOINTS.accountClosure.getClosureRequests;
  return ApiService.makeRequest(reqObj, params);
};

const updateRequestStatus = (params) => {
  const reqObj = ENDPOINTS.accountClosure.updateRequestStatus;
  return ApiService.makeRequest(reqObj, params);
};

const getUserDetails = (params) => {
  const reqObj = ENDPOINTS.accountClosure.getUserDetails;
  return ApiService.makeRequest(reqObj, params);
};

const getClosureForm = (params) => {
  const reqObj = ENDPOINTS.accountClosure.getClosureForm;
  return ApiService.makeRequest(reqObj, params);
};

export {
  getClosureRequests,
  updateRequestStatus,
  getUserDetails,
  getClosureForm,
};
