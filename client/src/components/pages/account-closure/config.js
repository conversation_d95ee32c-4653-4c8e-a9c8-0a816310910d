import React from 'react';
import { PtmButton } from '@paytm-money/frontend-common-v2';
import moment from 'moment';
import ActionField from './partials/ActionField';

const navList = [
  {
    title: 'CS',
  },
  {
    title: 'OPS',
  },
];

const statusList = {
  CS: [
    'RETAINED', 'ASSIGNED_TO_CS', 'REASSIGNED_TO_CS',
  ],
  OPS: [
    'CLOSED', 'ASSIGNED_TO_OPS',
  ],
};

const possibleStatusList = {
  ASSIGNED_TO_CS: ['ASSIGNED_TO_OPS', 'RETAINED'],
  ASSIGNED_TO_OPS: ['REASSIGNED_TO_CS', 'CLOSED'],
  REASSIGNED_TO_CS: ['ASSIGNED_TO_OPS', 'RETAINED'],
  CLOSED: [],
  RETAINED: [],
};

const actionColumnFields = {
  CS: {
    inputPlaceHolder: 'Input for feedback from <PERSON><PERSON>',
    firstButtonLabel: 'Retained',
    firstButtonStatus: 'RETAINED',
    secondButtonLabel: 'Assign to OPS',
    secondButtonStatus: 'ASSIGNED_TO_OPS',
  },
  OPS: {
    inputPlaceHolder: 'Input to reassign to CS',
    firstButtonLabel: 'Reassign to CS',
    firstButtonStatus: 'REASSIGNED_TO_CS',
    secondButtonLabel: 'Closed',
    secondButtonStatus: 'CLOSED',
  },
};

export const formatDate = (date) => {
  try {
    return moment(date).format('YYYY/MM/DD hh:mm a');
  } catch (error) {
    return date;
  }
};

const userDetailsInfo = [
  {
    name: 'Demat Account Number',
    field: 'dematAcc',
  },
  {
    name: 'Name of the client',
    field: 'name',
  },
  {
    name: 'Address of the correspondence',
    field: 'address',
  },
  {
    name: 'City',
    field: 'city',
  },
  {
    name: 'State',
    field: 'state',
  },
  {
    name: 'Pin Code',
    field: 'pinCode',
  },
  {
    name: 'Fund Balance',
    field: 'fundBalance',
  },
  {
    name: 'Stock Portfolio',
    field: 'stockPortfolio',
  },
];

const tableConfig = (actionColumnValues) => [
  {
    title: 'User ID',
    dataField: 'userId',
    width: '10%',
    dataFormat: (userId) => (
      // eslint-disable-next-line react/jsx-filename-extension
      <a
        href={`/customer-details/${userId}/basic-details`}
        target="_blank"
        rel="noopener noreferrer"
      >
        {userId}
      </a>
    ),
  },
  {
    title: 'Status',
    dataField: 'status',
    width: '10%',
  },
  {
    title: 'Created At',
    dataField: 'createdAt',
    width: '10%',
    dataFormat: (createdAt) => formatDate(createdAt),
  },
  {
    title: 'Closure Form',
    dataField: 'closureFormUrl',
    dataFormat: (closureFormUrl, data) => (
      // eslint-disable-next-line react/jsx-filename-extension
      <>
        {closureFormUrl
          && (
          <PtmButton
            appearance={'primaryDark'}
            handlePress={() => actionColumnValues.openModal(data)}
          >
            View
          </PtmButton>
          )}
      </>
    ),
    width: '10%',
  },
  {
    title: 'Action',
    width: '35%',
    dataField: 'closureRequestId',
    dataFormat: (closureRequestId, closureRequestObj) => (
      <ActionField
        {...actionColumnValues}
        actionColumnFields={actionColumnFields}
        data={closureRequestObj}
        possibleStatusList={possibleStatusList}
      />
    ),
  },
];

export {
  navList,
  statusList,
  possibleStatusList,
  tableConfig,
  actionColumnFields,
  userDetailsInfo,
};
