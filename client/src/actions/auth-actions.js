import ApiService from '../services/ApiService';
import { ENDPOINTS } from '../constants/api-constants';

export const LOGIN_SUCCESS = 'LOGIN_SUCCESS';
export const REQUEST_LOGIN = 'REQUEST_LOGIN';
export const LOGIN_ERROR = 'LOGIN_ERROR';
export const SESSION_FETCHED = 'SESSION_FETCHED';
export const LOGOUT = 'LOGOUT';
export const LOGOUT_ERROR = 'LOGOUT_ERROR';
export const SESSION_FETCH_ERROR = 'SESSION_FETCH_ERROR';

// This action creator is called every time the page reloads
export const fetchSession = () => (dispatch) => 
ApiService.makeRequest(ENDPOINTS.auth.fetchSession)  
.then((response) => {
    let isLoggedIn = true;
    let loggedInUser = response;
    if (response.status) {
      isLoggedIn = false;
      loggedInUser = null;
    }
      dispatch({
        type: SESSION_FETCHED,
        payload: {
          isLoggedIn,
          loggedInUser,
        },
      });
  })
  .catch(() => {
    dispatch({
      type: SESSION_FETCH_ERROR,
    });
  });

export const logoutActionV2 = (userId) => (dispatch) => {
  const params = { userId };
  ApiService.makeRequest(ENDPOINTS.auth.logoutV2, params).then((response) => {
    dispatch({ type: LOGOUT });
  }).catch((err) => {
    dispatch({ type: LOGOUT_ERROR, payload: err.error });
  });
};

export const loginSuccess = (userData) => (dispatch) => {
  // dispatch({ type: LOGIN_SUCCESS, payload: userData });
  ApiService.makeRequest(ENDPOINTS.auth.fetchSession)
    .then((response) => {
      dispatch({ type: LOGIN_SUCCESS, payload: userData });
    })
    .catch(() => {
      dispatch({
        type: SESSION_FETCH_ERROR,
      });
    });
};
