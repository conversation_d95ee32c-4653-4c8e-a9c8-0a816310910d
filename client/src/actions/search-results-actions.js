import { fetchSearchResultsData } from '../api-util';

export const GET_SEARCH_RESULTS_DATA = 'GET_SEARCH_RESULTS_DATA';
export const GET_SEARCH_RESULTS_REQUEST = 'GET_SEARCH_RESULTS_REQUEST';
export const GET_SEARCH_RESULTS_FAILURE = 'GET_SEARCH_RESULTS_FAILURE';

const onSuccessCB = searchResults => ({
  type: GET_SEARCH_RESULTS_DATA,
  payload: searchResults,
});

const onErrorCB = error => ({
  type: GET_SEARCH_RESULTS_FAILURE,
  payload: error,
});

export const getSearchResults = params => (dispatch) => {
  dispatch({
    type: GET_SEARCH_RESULTS_REQUEST,
  });
  fetchSearchResultsData(
    params,
    searchResults => dispatch(onSuccessCB(searchResults)),
    ({ error }) => dispatch(onErrorCB(error)),
  );
};
