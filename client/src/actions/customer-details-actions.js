import ApiService from '../services/ApiService';
import { ENDPOINTS } from '../constants/api-constants';

export const RESET_CUSTOMER_DETAILS = 'RESET_CUSTOMER_DETAILS';
export const REQUEST_CUSTOMER_DETAILS = 'REQUEST_BASIC_DETAILS';
export const REQUEST_CUSTOMER_DETAILS_SUCCESS = 'REQUEST_CUSTOMER_DETAILS_SUCCESS';
export const REQUEST_CUSTOMER_DETAILS_FAILURE = 'REQUEST_CUSTOMER_DETAILS_FAILURE';
export const UPDATE_FIELD_APPROVAL_META = 'UPDATE_FIELD_APPROVAL_META';
export const UPDATE_EQUITY_FIELD_APPROVAL_META = 'UPDATE_EQUITY_FIELD_APPROVAL_META';
export const UPDATE_INCIDENT_META = 'UPDATE_INCIDENT_META';
export const UPDATE_ONE_TIME_MENDATE_META = 'UPDATE_ONE_TIME_MENDATE_META';
export const UPDATE_PAYMENT_MODE_META = 'UPDATE_PAYMENT_MODE_META';

export const fetchBasicDetails = (userId) => (dispatch) => {
  dispatch({
    type: REQUEST_CUSTOMER_DETAILS,
    payload: userId,
  });

  ApiService.makeRequest(ENDPOINTS.customer.getCustomerInvestmentDetails, null, userId)
  .then((res) => {
    ApiService.makeRequest(ENDPOINTS.customer.getBasicCustomerDetails, null, userId)
    .then((newRes) => {
      dispatch({
        type: REQUEST_CUSTOMER_DETAILS_SUCCESS,
        payload: { ...newRes,...res },
      });
    })
    .catch(({ error }) => {
      dispatch({
        type: REQUEST_CUSTOMER_DETAILS_FAILURE,
        payload: error,
      });
    });
  })
  .catch(({ error }) => {
    dispatch({
      type: REQUEST_CUSTOMER_DETAILS_FAILURE,
      payload: error,
    });
  });
};

export const fetchInvestmentDetails = (userId) => (dispatch) => {
  dispatch({
    type: REQUEST_CUSTOMER_DETAILS,
    payload: userId,
  });
  // TODO: Handle error cases
};

export const updatefieldApprovalMeta = (metaData) => (dispatch) => {
  dispatch({
    type: UPDATE_FIELD_APPROVAL_META,
    payload: metaData,
  });
};

export const updateEquityFieldApprovalMeta = (metaData) => (dispatch) => {
  dispatch({
    type: UPDATE_EQUITY_FIELD_APPROVAL_META,
    payload: metaData,
  });
};

export const updateIncidentMeta = (metaData) => (dispatch) => {
  dispatch({
    type: UPDATE_INCIDENT_META,
    payload: metaData,
  });
};

export const updateoneTimeMandateMeta = (metaData) => (dispatch) => {
  dispatch({
    type: UPDATE_ONE_TIME_MENDATE_META,
    payload: metaData,
  });
};

export const updatePaymentModeMeta = (metaData) => (dispatch) => {
  dispatch({
    type: UPDATE_PAYMENT_MODE_META,
    payload: metaData,
  });
};

export const resetCustomerDetailsData = () => (dispatch) => {
  dispatch({
    type: RESET_CUSTOMER_DETAILS,
  });
};
