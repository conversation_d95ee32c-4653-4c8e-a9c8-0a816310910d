import { ENDPOINTS } from '../constants/api-constants';
import ApiService from '../services/ApiService';

export const REQUEST_READINESS_DETAILS = 'REQUEST_READINESS_DETAILS';
function requestReadinessDetails() {
  return {
    type: 'REQUEST_READINESS_DETAILS',
  };
}

export const RECEIVE_READINESS_DETAILS_SUCCESS = 'RECEIVE_READINESS_DETAILS_SUCCESS';
function receiveReadinessDetails(readinessDetails) {
  return {
    type: RECEIVE_READINESS_DETAILS_SUCCESS,
    payload: readinessDetails,
  };
}

export const RECEIVE_READINESS_DETAILS_ERROR = 'RECEIVE_READINESS_DETAILS_ERROR';
function receiveReadinessDetailsError(error) {
  return {
    type: RECEIVE_READINESS_DETAILS_ERROR,
    payload: error,
  };
}

function requestEquityReadinessDetails() {
  return {
    type: 'REQUEST_EQUITY_READINESS_DETAILS',
  };
}

export const RECEIVE_EQUITY_READINESS_DETAILS_SUCCESS = 'RECEIVE_EQUITY_READINESS_DETAILS_SUCCESS';
function receiveEquityReadinessDetails(readinessDetails) {
  return {
    type: RECEIVE_EQUITY_READINESS_DETAILS_SUCCESS,
    payload: readinessDetails,
  };
}

export const RECEIVE_EQUITY_READINESS_DETAILS_ERROR = 'RECEIVE_EQUITY_READINESS_DETAILS_ERROR';
function receiveEquityReadinessDetailsError(error) {
  return {
    type: RECEIVE_READINESS_DETAILS_ERROR,
    payload: error,
  };
}

function requestNpsReadinessDetails() {
  return {
    type: 'REQUEST_NPS_READINESS_DETAILS',
  };
}

export const RECEIVE_NPS_READINESS_DETAILS_SUCCESS = 'RECEIVE_NPS_READINESS_DETAILS_SUCCESS';
function receiveNpsReadinessDetails(readinessDetails) {
  return {
    type: RECEIVE_NPS_READINESS_DETAILS_SUCCESS,
    payload: readinessDetails,
  };
}

export const RECEIVE_NPS_READINESS_DETAILS_ERROR = 'RECEIVE_NPS_READINESS_DETAILS_ERROR';
function receiveNpsReadinessDetailsError(error) {
  return {
    type: RECEIVE_READINESS_DETAILS_ERROR,
    payload: error,
  };
}


export const REQUEST_NFT_APPROVAL_DETAILS = 'REQUEST_NFT_APPROVAL_DETAILS';
function requestNftApprovalDetails() {
  return {
    type: REQUEST_NFT_APPROVAL_DETAILS,
  };
}

export const RECEIVE_NFT_APPROVAL_DETAILS_SUCCESS = 'RECEIVE_NFT_APPROVAL_DETAILS_SUCCESS';
function receiveNftApprovalDetails(readinessDetails) {
  return {
    type: RECEIVE_NFT_APPROVAL_DETAILS_SUCCESS,
    payload: readinessDetails,
  };
}

export const RECEIVE_NFT_APPROVAL_DETAILS_ERROR = 'RECEIVE_NFT_APPROVAL_DETAILS_ERROR';
function receiveNftApprovalDetailsError(error) {
  return {
    type: RECEIVE_NFT_APPROVAL_DETAILS_ERROR,
    payload: error,
  };
}

// TODO: Handle error cases
export const fetchReadinessDetails = (customerId, agentId) => (dispatch) => {
  dispatch(requestReadinessDetails());
  const { getInvestmentReadiness } = ENDPOINTS.customer;
  ApiService.makeRequest(getInvestmentReadiness, {}, customerId, {})
    .then((response) => {
      dispatch(receiveReadinessDetails(response));
    })
    .catch(({ error }) => {
      dispatch(receiveReadinessDetailsError(error));
    });
};

export const fetchEqReadinessDetails = (customerId, agentId, ticketType) => (dispatch) => {
  dispatch(requestEquityReadinessDetails());
  const { getEqInvestmentReadiness } = ENDPOINTS.customer;
  ApiService.makeRequest(getEqInvestmentReadiness, {}, [customerId, ticketType], {})
    .then((response) => {
      dispatch(receiveEquityReadinessDetails(response));
    })
    .catch(({ error }) => {
      dispatch(receiveEquityReadinessDetailsError(error));
    });
};

export const fetchNpsReadinessDetails = (customerId, agentId) => (dispatch) => {
  dispatch(requestNpsReadinessDetails());
  const { getNpsInvestmentReadiness } = ENDPOINTS.customer;
  ApiService.makeRequest(getNpsInvestmentReadiness, {}, customerId, {})
    .then((response) => {
      dispatch(receiveNpsReadinessDetails(response));
    })
    .catch(({ error }) => {
      dispatch(receiveNpsReadinessDetailsError(error));
    });
};

export const fetchNftApprovalDetails = (customerId, agentId) => (dispatch) => {
  dispatch(requestNftApprovalDetails());
  const { fetchNftDetails } = ENDPOINTS.customer;
  ApiService.makeRequest(fetchNftDetails, {}, customerId, {})
    .then((response) => {
      dispatch(receiveNftApprovalDetails(response));
    })
    .catch(({ error }) => {
      dispatch(receiveNftApprovalDetailsError(error));
    });
};
