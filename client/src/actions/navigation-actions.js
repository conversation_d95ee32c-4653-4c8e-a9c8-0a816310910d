// Actions:
export const UPDATE_SELECTED_TAB = 'UPDATE_SELECTED_TAB';
export const UPDATE_SELECTED_CUSTOMER_TAB = 'UPDATE_SELECTED_CUSTOMER_TAB';
export const TOGGLE_NAV_BAR_DISPLAY = 'TOGGLE_NAV_BAR_DISPLAY';
export const TOGGLE_SUBMENU = 'TOGGLE_SUBMENU';


export function updateSelectedTab(navLinkId, subLinkId = null) {
  return {
    type: UPDATE_SELECTED_TAB,
    payload: {
      navLinkId,
      subLinkId,
    },
  };
}

export function updateSelectedCustomerTab(id) {
  return {
    type: UPDATE_SELECTED_CUSTOMER_TAB,
    payload: id,
  };
}

export function toggleNavBarDisplay() {
  return {
    type: TOGGLE_NAV_BAR_DISPLAY,
  };
}

export function toggleSubmenu(id) {
  return {
    type: TOG<PERSON><PERSON>_SUBMENU,
    payload: id,
  };
}
