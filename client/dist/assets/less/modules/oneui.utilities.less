//
// Utilities
// --------------------------------------------------

// Layout
.push-5 {
    .push-generate(5px);
}

.push-10 {
    .push-generate(10px);
}

.push-15 {
    .push-generate(15px);
}

.push-20 {
    .push-generate(20px);
}

.push-30 {
    .push-generate(30px);
}

.push-50 {
    .push-generate(50px);
}

.push-100 {
    .push-generate(100px);
}

.push-150 {
    .push-generate(150px);
}

.push-200 {
    .push-generate(200px);
}

.push-300 {
    .push-generate(300px);
}

.pulldown {
    position: relative;
    top: 50px;

    @media screen and (min-width: @screen-md-min) {
        top: 150px;
    }
}

.remove-margin {
    margin: 0 !important;

    &-t { margin-top: 0 !important; }
    &-r { margin-right: 0 !important; }
    &-b { margin-bottom: 0 !important; }
    &-l { margin-left: 0 !important; }
}

.remove-padding {
    padding: 0 !important;

    &-t { padding-top: 0 !important; }
    &-r { padding-right: 0 !important; }
    &-b { padding-bottom: 0 !important; }
    &-l { padding-left: 0 !important; }
}

// Height helpers
.mheight-50 { min-height: 50px; }
.mheight-75 { min-height: 75px; }
.mheight-100 { min-height: 100px; }
.mheight-125 { min-height: 125px; }
.mheight-150 { min-height: 150px; }
.mheight-175 { min-height: 175px; }
.mheight-200 { min-height: 200px; }

// Vertical center align
.align-v {
    .vertical-align();

    &&-fwidth {
        width: 100%;
    }
}

// Border
.border {
    border: 1px solid #e9e9e9;

    &-t { border-top: 1px solid #e9e9e9; }
    &-r { border-right: 1px solid #e9e9e9; }
    &-b { border-bottom: 1px solid #e9e9e9; }
    &-l { border-left: 1px solid #e9e9e9; }
}

.border-white-op {
    border: 1px solid rgba(255,255,255,.1);

    &-t { border-top: 1px solid rgba(255,255,255,.1); }
    &-r { border-right: 1px solid rgba(255,255,255,.1); }
    &-b { border-bottom: 1px solid rgba(255,255,255,.1); }
    &-l { border-left: 1px solid rgba(255,255,255,.1); }
}

.border-black-op {
    border: 1px solid rgba(0,0,0,.1);

    &-t { border-top: 1px solid rgba(0,0,0,.1); }
    &-r { border-right: 1px solid rgba(0,0,0,.1); }
    &-b { border-bottom: 1px solid rgba(0,0,0,.1); }
    &-l { border-left: 1px solid rgba(0,0,0,.1); }
}

// Item
.item {
    display: inline-block;
    width: 60px;
    height: 60px;
    text-align: center;
    font-size: 28px;
    font-weight: 300;
    line-height: 60px;

    a& {
        &:hover,
        &:focus {
            opacity: .6;
        }
    }

    &&-circle {
        border-radius: 50%;
    }

    &&-rounded {
        border-radius: 4px;
    }

    &&-rounded-big {
        border-radius: 24px;

        &.item-2x {
            border-radius: 35px;
        }

        &.item-3x {
            border-radius: 50px;
        }
    }

    &.item-2x {
        width: 100px;
        height: 100px;
        line-height: 100px;
    }

    &.item-3x {
        width: 150px;
        height: 150px;
        line-height: 150px;
    }
}

// Ribbons
.ribbon {
    position: relative;
    min-height: 56px;

    &-box {
        position: absolute;
        top: 10px;
        right: 0;
        padding: 0 15px;
        height: 36px;
        line-height: 36px;
        color: #fff;
        z-index: 500;

        &:before {
            position: absolute;
            display: block;
            width: 0;
            height: 0;
            content: "";
        }
    }

    // Styles
    &-bookmark {
        .ribbon-box {
            padding-left: 0;
        }

        .ribbon-box:before {
            top: 0;
            right: 100%;
            border: 18px solid;
            border-left-width: 10px;
        }
    }

    &-modern {
        .ribbon-box {
            top: 0;
        }

        .ribbon-box:before {
            top: 0;
            right: 100%;
            border: 18px solid;
        }
    }

    // Position
    &-left {
        .ribbon-box {
            right: auto;
            left: 0;
        }

        &.ribbon-bookmark {
            .ribbon-box {
                padding-left: 15px;
                padding-right: 0;
            }

            .ribbon-box:before {
                right: auto;
                left: 100%;
                border-left-width: 18px;
                border-right-width: 10px;
            }
        }

        &.ribbon-modern {
            .ribbon-box:before {
                right: auto;
                left: 100%;
            }
        }
    }

    &-bottom {
        .ribbon-box {
            top: auto;
            bottom: 10px;
        }

        &.ribbon-modern .ribbon-box {
            bottom: 0;
        }
    }

    // Colors
    &-primary {
        .ribbon-variation(@brand-primary);
    }

    &-success {
        .ribbon-variation(@brand-success);
    }

    &-info {
        .ribbon-variation(@brand-info);
    }

    &-warning {
        .ribbon-variation(@brand-warning);
    }

    &-danger {
        .ribbon-variation(@brand-danger);
    }

    &-crystal {
        .ribbon-box {
            background-color: rgba(255,255,255,.35);
        }

        &.ribbon-bookmark {
            .ribbon-box:before {
                border-color: rgba(255,255,255,.35);
                border-left-color: transparent;
            }

            &.ribbon-left .ribbon-box:before {
                border-color: rgba(255,255,255,.35);
                border-right-color: transparent;
            }
        }

        &.ribbon-modern {
            .ribbon-box:before {
                border-color: rgba(255,255,255,.35);
                border-left-color: transparent;
                border-bottom-color: transparent;
            }

            &.ribbon-bottom .ribbon-box:before {
                border-color: rgba(255,255,255,.35);
                border-top-color: transparent;
                border-left-color: transparent;
            }

            &.ribbon-left .ribbon-box:before {
                border-color: rgba(255,255,255,.35);
                border-right-color: transparent;
                border-bottom-color: transparent;
            }

            &.ribbon-left.ribbon-bottom .ribbon-box:before {
                border-color: rgba(255,255,255,.35);
                border-top-color: transparent;
                border-right-color: transparent;
            }
        }
    }
}

// Helpers
.overflow-hidden {
    overflow: hidden;
}

.overflow-y-auto {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

.visibility-hidden {
    visibility: hidden;
}

// IE9
.visible-ie9 {
    display: none;
}

.ie9 {
    .hidden-ie9 {
        display: none !important;
    }

    .visible-ie9 {
        display: block;
    }

    .visible-ie9-ib {
        display: inline-block;
    }
}