{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "develpoment",
      "program": "${workspaceFolder}/server.js",
      "runtimeExecutable": "~/.nvm/versions/node/v9.11.2/bin/node",
      "env": {
        "NODE_ENV": "local"
      }
    },
    {
      "type": "node",
      "request": "launch",
      "name": "staging",
      "program": "${workspaceFolder}/server.js",
      //"runtimeExecutable": "~/.nvm/versions/node/v9.11.2/bin/node",
      "env": {
        "NODE_ENV": "localStaging"
      }
    },
    {
      "type": "node",
      "request": "launch",
      "name": "preprod",
      "program": "${workspaceFolder}/server.js",
      "runtimeExecutable": "~/.nvm/versions/node/v9.11.2/bin/node",
      "env": {
        "NODE_ENV": "localPreprod"
      }
    }
  ]
}